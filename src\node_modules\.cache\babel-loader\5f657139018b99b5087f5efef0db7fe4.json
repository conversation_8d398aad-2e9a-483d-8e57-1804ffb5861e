{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js??ref--6!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue?vue&type=template&id=302736bc&scoped=true", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue", "mtime": 1751445617508}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_imports_0", "_imports_1", "_imports_2", "class", "style", "$data", "isshow", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_createVNode", "_component_el_select", "selectvalue1", "$event", "placeholder", "onChange", "_ctx", "handleChange", "_Fragment", "_renderList", "options1", "item", "_createBlock", "_component_el_option", "key", "value", "label", "selectvalue2", "options2", "selectvalue3", "options3", "onClick", "_cache", "$options", "anniu", "src", "alt", "_hoisted_5", "_hoisted_6", "_component_Titles", "tit", "_hoisted_7", "_toDisplayString", "totalConsumption", "total", "toFixed", "_hoisted_8", "_hoisted_9", "_hoisted_10", "daily", "_createCommentVNode", "_hoisted_11", "_hoisted_12", "_hoisted_13", "weekly", "_hoisted_14", "_hoisted_15", "_hoisted_16", "monthly", "_hoisted_17", "_hoisted_18", "_component_Electricity3", "electricityUsageData", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_el_table", "data", "meterReadings", "height", "_component_el_table_column", "prop", "align", "width", "_hoisted_22", "_hoisted_23", "args", "showDetailDialog", "_hoisted_24", "dialogVisible", "_withModifiers", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_button", "type", "_hoisted_28", "_hoisted_29", "_component_el_date_picker", "date<PERSON><PERSON><PERSON>", "searchData", "detailData"], "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue"], "sourcesContent": ["<template>\n  <div class=\"contents\" v-if=\"isshow\">\n    <div class=\"toubu\">\n      <div\n        style=\"margin-left: 20px; display: flex; align-items: center\"\n        v-if=\"false\"\n      >\n        <div style=\"display: flex; width: 100%; align-items: center\">\n          <span class=\"sp\">当前位置：</span>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue1\"\n            placeholder=\"selectvalue1\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options1\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue2\"\n            placeholder=\"selectvalue2\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options2\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue3\"\n            placeholder=\"selectvalue3\"\n            style=\"width: 78px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options3\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </div>\n        <img\n          v-if=\"isshow\"\n          class=\"img1sss\"\n          @click=\"anniu()\"\n          src=\"../assets/image/table-x.png\"\n          alt=\"\"\n        />\n      </div>\n\n      <div class=\"all\">\n        <div class=\"all1\">\n          <Titles class=\"ltitle\" tit=\"大型仪器平台概况\">\n            <div class=\"nenghao\">累计总能耗:</div>\n            <p class=\"nhp\">{{ totalConsumption.total.toFixed(1) }} kwh</p>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.daily.toFixed(1) }}kwh</p>\n                <p class=\"p2\">本日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>  \n                <p class=\"p2\">近7日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao2.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p12\">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>\n                <p class=\"p2\">近30日累计能耗</p>\n              </div>\n              <div class=\"nht\">\n                <!-- <div class=\"nhtit1\">\n                  <img\n                    class=\"nhimg1\"\n                    src=\"../assets/image/nhshang.png\"\n                    alt=\"\"\n                  />\n                  <p class=\"pp2\">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>\n                </div> -->\n                <!-- <p class=\"pp\">环比</p> -->\n              </div>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle\" style=\"margin-top: 20px\" tit=\"电耗费用\">\n            <div class=\"shinei\">\n              <Electricity2\n                :yesterday-fee=\"electricityFees.yesterday\"\n                :monthly-fee=\"electricityFees.monthly\"\n                :yearly-fee=\"electricityFees.yearly\"\n              ></Electricity2>\n            </div>\n          </Titles> -->\n        </div>\n        <!-- <div class=\"line1\"></div> -->\n        <div class=\"all2\">\n          <!-- <Titles class=\"ltitle1\" tit=\"峰平谷用电量\">\n            <div class=\"shinei\">\n              <Electricity8></Electricity8>\n            </div>\n          </Titles> -->\n\n          <Titles class=\"ltitle1\" tit=\"用电量排名\">\n            <div class=\"shinei\">\n              <Electricity3\n                :electricity-data=\"electricityUsageData\"\n              ></Electricity3>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle1\" tit=\"分区用电量\">\n            <div class=\"shinei\">\n              <Electricity4 :fee-data=\"electricityFeeData\"></Electricity4>\n            </div>\n          </Titles> -->\n        </div>\n        <div class=\"all3\">\n          <div>\n            <Titles class=\"ltitle1\" tit=\"抄电表记录\">\n              <div class=\"shinei\">\n                <div class=\"table-container\">\n                  <el-table\n                    :data=\"meterReadings\"\n                    style=\"width: 100%; background: transparent\"\n                    :header-cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    :cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    height=\"320\"\n                  >\n                    <el-table-column\n                      prop=\"roomtag\"\n                      label=\"房间标识\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"zhaddress\"\n                      label=\"住户地址\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                    <el-table-column\n                      prop=\"readvalue\"\n                      label=\"抄表值\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"readtime\"\n                      label=\"抄表时间\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                  </el-table>\n                </div>\n              </div>\n            </Titles>\n          </div>\n          <Titles class=\"ltitle1\" style=\"margin-top:15px\" tit=\"用电量记录\">\n            <div class=\"shinei\">\n              <div class=\"title-container\">\n                <div class=\"more-btn\" @click=\"showDetailDialog\">\n                  <span>更多</span>\n                  <i class=\"el-icon-arrow-right\"></i>\n                </div>\n              </div>\n              <div class=\"table-container\">\n                <el-table\n                  :data=\"electricityUsageData\"\n                  style=\"width: 100%; background: transparent\"\n                  :header-cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#38444C',\n                  }\"\n                  :cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#1e415c',\n                  }\"\n                  height=\"400\"\n                >\n                  <el-table-column\n                    prop=\"roomtag\"\n                    label=\"房间标识\"\n                    align=\"center\"\n                    width=\"120\"\n                  />\n                  <el-table-column\n                    prop=\"zhaddress\"\n                    label=\"住户地址\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                  <el-table-column\n                    prop=\"ylvalue\"\n                    label=\"用电量(kwh)\"\n                    align=\"center\"\n                    width=\"120\"\n                  >\n                    <!-- <template slot-scope=\"scope\">\n                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}\n                    </template> -->\n                  </el-table-column>\n                  <el-table-column\n                    prop=\"endtime\"\n                    label=\"抄表时间\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                </el-table>\n              </div>\n            </div>\n          </Titles>\n        </div>\n      </div>\n    </div>\n\n    <!-- 自定义弹窗 -->\n    <div\n      v-if=\"dialogVisible\"\n      class=\"custom-modal-overlay\"\n      @click.self=\"dialogVisible = false\"\n    >\n      <div class=\"custom-modal\">\n        <div class=\"modal-header\">\n          <span class=\"modal-title\">用电量详细记录</span>\n          <div class=\"header-buttons\">\n            <el-button\n              type=\"text\"\n              class=\"close-text\"\n              @click=\"dialogVisible = false\"\n              >关闭</el-button\n            >\n            <i\n              class=\"el-icon-close close-btn\"\n              @click=\"dialogVisible = false\"\n            ></i>\n          </div>\n        </div>\n        <div class=\"modal-content\">\n          <!-- 搜索条件 -->\n          <div class=\"search-container\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              value-format=\"YYYY-MM-DD\"\n              style=\"width: 380px; margin-right: 15px\"\n            >\n            </el-date-picker>\n            <!-- <el-date-picker\n                v-model=\"dateRange\"\n                type=\"daterange\"\n                align=\"right\"\n                unlink-panels\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n              > -->\n            <!-- </el-date-picker> -->\n\n            <el-button type=\"primary\" @click=\"searchData\">查询</el-button>\n            <!-- <el-button type=\"success\" @click=\"exportToExcel\">导出</el-button> -->\n          </div>\n\n          <!-- 详细数据表格 -->\n          <el-table\n            :data=\"detailData\"\n            style=\"width: 100%; margin-top: 20px\"\n            :header-cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            :cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            height=\"500\"\n          >\n            <el-table-column\n              prop=\"roomtag\"\n              label=\"房间标识\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"zhaddress\"\n              label=\"住户地址\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"startcode\"\n              label=\"起码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"endcode\"\n              label=\"止码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"ylvalue\"\n              label=\"用电量(kwh)\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"jfmx\"\n              label=\"缴费明细\"\n              align=\"center\"\n              width=\"180\"\n            />\n\n            <el-table-column\n              prop=\"endtime\"\n              label=\"抄表时间\"\n              align=\"center\"\n              width=\"181\"\n            />\n          </el-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as XLSX from \"xlsx\";\nimport Electricity from \"../components/echarts/dianbiao/biao1.vue\";\nimport biao1s from \"../components/echarts/dianbiao/biao1s.vue\";\nimport biao1ss from \"../components/echarts/dianbiao/biao1ss.vue\";\nimport Titles from \"../components/common/Titles.vue\";\n\nimport Electricity2 from \"../components/echarts/dianbiao/Electricity2.vue\";\nimport Electricity3 from \"../components/echarts/dianbiao/Electricity3.vue\";\nimport Electricity4 from \"../components/echarts/dianbiao/Electricity4.vue\";\nimport Electricity5 from \"../components/echarts/dianbiao/Electricity5.vue\";\nimport Electricity6 from \"../components/echarts/dianbiao/Electricity6.vue\";\nimport Electricity7 from \"../components/echarts/dianbiao/Electricity7.vue\";\nimport Electricity8 from \"../components/echarts/dianbiao/Electricity8.vue\";\nimport huanxing from \"@/components/echarts/xiaobingtu.vue\";\nimport axios from \"axios\";\nimport { buildingEnergyDataList } from \"@/api/device\";\n\nexport default {\n  components: {\n    Titles,\n    Electricity,\n    Electricity2,\n    Electricity3,\n    Electricity4,\n    Electricity5,\n    Electricity6,\n    Electricity7,\n    Electricity8,\n    huanxing,\n    biao1s,\n    biao1ss,\n  },\n  data() {\n    return {\n      isshow: true,\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"最近一周\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近一个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近三个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n        ],\n      },\n      options: [\n        {\n          value: \"总览\",\n          label: \"总览\",\n        },\n        {\n          value: \"能耗分析\",\n          label: \"能耗分析\",\n        },\n        {\n          value: \"能流分析\",\n          label: \"能流分析\",\n        },\n        {\n          value: \"设备状态\",\n          label: \"设备状态\",\n        },\n        {\n          value: \"一键抄表\",\n          label: \"一键抄表\",\n        },\n        {\n          value: \"费用管理\",\n          label: \"费用管理\",\n        },\n        {\n          value: \"碳排放管理\",\n          label: \"碳排放管理\",\n        },\n      ],\n      selectvalue2: \"B3\",\n      options2: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B3\",\n      options3: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue1: \"B3\",\n      options1: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B1栋\",\n      options4: [\n        {\n          value: \"B1栋\",\n          label: \"B1栋\",\n        },\n        {\n          value: \"B2栋\",\n          label: \"B2栋\",\n        },\n        {\n          value: \"B3栋\",\n          label: \"B3栋\",\n        },\n        {\n          value: \"B4栋\",\n          label: \"B4栋\",\n        },\n        {\n          value: \"W1栋\",\n          label: \"W1栋\",\n        },\n        {\n          value: \"W2栋\",\n          label: \"W2栋\",\n        },\n      ],\n      selectvalue4: \"B1栋\",\n      optionData: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      optionData1: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      token: {\n        systemnum: \"\",\n        tokenvalue: \"\",\n        expiretime: \"\",\n      },\n      baseURL: \"/power\", // Replace with actual server address\n      // baseURL: 'http://*************:8080',\n      isTokenValid: false,\n      totalConsumption: {\n        daily: 0,\n        weekly: 0,\n        monthly: 0,\n        total: 0, // 累计用量\n      },\n      curBuilding: null, // 当前建筑信息\n      totalElectricityFee: 0,\n      roomtag: \"\", // Add your roomtag here if you want to query specific user\n      electricityFees: {\n        yesterday: 0,\n        monthly: 0,\n        yearly: 0,\n      },\n      meterReadings: [], // 新增抄表记录数据\n      electricityUsageData: [], // 新增用电量数据\n      \n      dialogVisible: false, // 修改为 false，默认关闭\n      dateRange: \"\",\n      detailData: [],\n      electricityFeeData: [], // 新增电费数据\n    };\n  },\n  methods: {\n    anniu() {\n      this.isshow = false;\n    },\n\n    // 构建API参数 - 参考 BimEnergyOverview.vue 的实现\n    buildParams(type) {\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.error('curBuilding 未初始化');\n        return null;\n      }\n\n      const param = {\n        buildingId: this.curBuilding.id,\n        deviceType: 'electricity',\n        type: 'electricity',\n        displayType: \"day\",\n        from: this.$moment().startOf(\"day\").format(\"YYYY-MM-DD\"),\n        to: this.$moment().format(\"YYYY-MM-DD\"),\n      };\n\n      // 根据类型调整参数\n      switch (type) {\n        case 'daily':\n          // 本日用量：昨天到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(1, 'day').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'weekly':\n          // 近7日用量：7天前到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(7, 'days').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'monthly':\n          // 近30日用量：30天前到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(30, 'days').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'total':\n          // 累计用量：从很久以前到今天\n          param.displayType = 'total';\n          param.from = this.$moment().subtract(10, 'years').startOf(\"year\").format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n      }\n\n      return param;\n    },\n\n    // 使用新API获取能耗数据\n    async getEnergyDataByType(type) {\n      const param = this.buildParams(type);\n      if (!param) {\n        return 0;\n      }\n\n      try {\n        const res = await buildingEnergyDataList(param);\n        let total = 0;\n        if (res.data && res.data.datas) {\n          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);\n        }\n        return total;\n      } catch (error) {\n        console.error(`获取${type}数据失败:`, error);\n        return 0;\n      }\n    },\n    async getToken() {\n      try {\n        const response = await axios.get(\n          `${this.baseURL}/api/ztwyPower/getToken`,\n          {\n            params: {\n              systemnum: \"346E473FD1EF46E3A2EE43F393BCAF7C\",\n            },\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const { systemnum, tokenvalue, expiretime } =\n            response.data.resultvalue;\n          this.token = {\n            systemnum,\n            tokenvalue,\n            expiretime,\n          };\n          this.isTokenValid = true;\n\n          // 存储 token 和获取时间\n          const tokenData = {\n            ...this.token,\n            timestamp: new Date().getTime(),\n          };\n          localStorage.setItem(\"powerToken\", JSON.stringify(tokenData));\n\n          console.log(\"Token updated successfully:\", this.token);\n        } else {\n          console.error(\"Failed to get token:\", response.data.errmsg);\n          this.isTokenValid = false;\n        }\n      } catch (error) {\n        console.error(\"Error getting token:\", error);\n        this.isTokenValid = false;\n      }\n    },\n    checkTokenValidity() {\n      const storedToken = localStorage.getItem(\"powerToken\");\n      if (storedToken) {\n        const tokenData = JSON.parse(storedToken);\n        const expireTime = new Date(tokenData.expiretime).getTime();\n        const currentTime = new Date().getTime();\n        const tokenTimestamp = tokenData.timestamp;\n\n        // 检查 token 是否过期或距离上次获取是否超过5分钟\n        if (\n          currentTime < expireTime &&\n          currentTime - tokenTimestamp < 5 * 60 * 1000\n        ) {\n          this.token = {\n            systemnum: tokenData.systemnum,\n            tokenvalue: tokenData.tokenvalue,\n            expiretime: tokenData.expiretime,\n          };\n          this.isTokenValid = true;\n          return true;\n        }\n      }\n      return false;\n    },\n    async getElectricityUsage(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag, // Optional: if empty, will return all users' data\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          // Calculate total consumption by summing ylvalue\n          const totalConsumption = response.data.resultvalue.reduce(\n            (sum, item) => {\n              return sum + parseFloat(item.ylvalue || 0);\n            },\n            0\n          );\n          return totalConsumption;\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n        return 0;\n      }\n    },\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\n      const day = String(date.getDate()).padStart(2, \"0\");\n      const hours = String(date.getHours()).padStart(2, \"0\");\n      const minutes = String(date.getMinutes()).padStart(2, \"0\");\n      const seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    async getPaymentStatistics(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const totalFee = response.data.resultvalue.reduce((sum, item) => {\n            return sum + parseFloat(item.zdf || 0);\n          }, 0);\n          return totalFee;\n        } else {\n          console.error(\n            \"Failed to get payment statistics:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting payment statistics:\", error);\n        return 0;\n      }\n    },\n    async updateConsumptionData() {\n      console.log(5685);\n      \n      // 检查 curBuilding 是否已初始化\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.error('curBuilding 未初始化，无法获取能耗数据');\n        return;\n      }\n\n      try {\n        // 使用新的API获取四个数据项\n        // 累计用量\n        this.totalConsumption.total = await this.getEnergyDataByType('total');\n\n        // 本日用量\n        this.totalConsumption.daily = await this.getEnergyDataByType('daily');\n\n        // 近7日用量\n        this.totalConsumption.weekly = await this.getEnergyDataByType('weekly');\n\n        // 近30日用量\n        this.totalConsumption.monthly = await this.getEnergyDataByType('monthly');\n\n        console.log('能耗数据更新成功:', this.totalConsumption);\n      } catch (error) {\n        console.error('更新能耗数据失败:', error);\n        // 如果新API失败，回退到原来的方法\n        await this.updateConsumptionDataFallback();\n      }\n    },\n\n    // 原来的数据获取方法作为备用\n    async updateConsumptionDataFallback() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const weekAgo = new Date(today);\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const monthAgo = new Date(today);\n      monthAgo.setDate(monthAgo.getDate() - 30);\n\n      // Get daily consumption\n      this.totalConsumption.daily = await this.getElectricityUsage(\n        this.formatDate(yesterday),\n        this.formatDate(now)\n      );\n\n      // Get weekly consumption\n      this.totalConsumption.weekly = await this.getElectricityUsage(\n        this.formatDate(weekAgo),\n        this.formatDate(now)\n      );\n\n      // Get monthly consumption\n      this.totalConsumption.monthly = await this.getElectricityUsage(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n\n      // 累计用量使用近30日数据作为近似值\n      this.totalConsumption.total = this.totalConsumption.monthly;\n\n      // Get total electricity fee\n      this.totalElectricityFee = await this.getPaymentStatistics(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n    },\n    async updateFeeData() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const yearStart = new Date(today.getFullYear(), 0, 1);\n\n      // Get yesterday's fee\n      this.electricityFees.yesterday = await this.getPaymentStatistics(\n        this.formatDate(yesterday),\n        this.formatDate(today)\n      );\n\n      // Get monthly fee\n      this.electricityFees.monthly = await this.getPaymentStatistics(\n        this.formatDate(monthStart),\n        this.formatDate(now)\n      );\n\n      // Get yearly fee\n      this.electricityFees.yearly = await this.getPaymentStatistics(\n        this.formatDate(yearStart),\n        this.formatDate(now)\n      );\n    },\n    async getMeterReadings() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getAllDbValue`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            tag: 0, // 返回最后一次抄表记录\n            nodeid: 0, // 默认为0，返回全部\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.meterReadings = response.data.resultvalue;\n          console.log(\n            \"Meter readings retrieved successfully:\",\n            this.meterReadings\n          );\n        } else {\n          console.error(\"Failed to get meter readings:\", response.data.errmsg);\n        }\n      } catch (error) {\n        console.error(\"Error getting meter readings:\", error);\n      }\n    },\n    async getElectricityUsageData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityUsageData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              ylvalue: parseFloat(item.ylvalue || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity usage data retrieved successfully:\",\n            this.electricityUsageData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n      }\n    },\n    showDetailDialog() {\n      this.dialogVisible = true;\n      this.detailData = []; // 清空数据\n      // 默认显示最近一天的数据\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天\n      this.dateRange = [\n        this.formatDate(start).split(\" \")[0],\n        this.formatDate(end).split(\" \")[0],\n      ];\n      this.searchData(); // 自动查询最近一天的数据\n    },\n    async searchData() {\n      if (!this.dateRange || this.dateRange.length !== 2) {\n        this.$message.warning(\"请选择日期范围\");\n        return;\n      }\n\n      try {\n        if (!this.isTokenValid) {\n          await this.getToken();\n        }\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: `${this.dateRange[0]} 00:00:00`,\n            endtime: `${this.dateRange[1]} 23:59:59`,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          if (\n            response.data.resultvalue &&\n            response.data.resultvalue.length > 0\n          ) {\n            this.detailData = response.data.resultvalue\n              .map((item) => ({\n                roomtag: item.roomtag || \"\",\n                zhaddress: item.zhaddress || \"\",\n                startcode: item.startcode\n                  ? parseFloat(item.startcode).toFixed(1)\n                  : \"0.0\",\n                endcode: item.endcode\n                  ? parseFloat(item.endcode).toFixed(1)\n                  : \"0.0\",\n                ylvalue: item.ylvalue\n                  ? parseFloat(item.ylvalue).toFixed(2)\n                  : \"0.00\",\n                jfmx: item.jfmx || \"\",\n                endtime: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n              }))\n              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));\n            this.$message.success(\"查询成功\");\n          } else {\n            this.detailData = [];\n            this.$message.warning(\"所选时间范围内无数据\");\n          }\n        } else {\n          this.$message.error(\"获取数据失败：\" + response.data.errmsg);\n          this.detailData = [];\n        }\n      } catch (error) {\n        this.$message.error(\"获取数据失败：\" + (error.message || \"未知错误\"));\n        this.detailData = [];\n      }\n    },\n    exportToExcel() {\n      if (!this.detailData || !this.detailData.length) {\n        this.$message.warning(\"暂无数据可导出\");\n        return;\n      }\n\n      try {\n        // 准备要导出的数据\n        const exportData = this.detailData.map((item) => ({\n          房间标识: item.roomtag || \"\",\n          住户地址: item.zhaddress || \"\",\n          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : \"0.0\",\n          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : \"0.0\",\n          \"用电量(kwh)\": item.ylvalue\n            ? parseFloat(item.ylvalue).toFixed(2)\n            : \"0.00\",\n          缴费明细: item.jfmx || \"\",\n          抄表时间: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n        }));\n\n        // 创建工作簿并设置数据\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"用电量记录\");\n\n        // 设置列宽\n        ws[\"!cols\"] = [\n          { wch: 15 }, // 房间标识\n          { wch: 20 }, // 住户地址\n          { wch: 12 }, // 起码\n          { wch: 12 }, // 止码\n          { wch: 15 }, // 用电量\n          { wch: 15 }, // 缴费明细\n          { wch: 20 }, // 抄表时间\n        ];\n\n        // 直接使用 XLSX.writeFile 导出文件\n        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;\n        XLSX.writeFile(wb, fileName);\n\n        this.$message.success(\"导出成功\");\n      } catch (error) {\n        console.error(\"Export error:\", error);\n        this.$message.error(\"导出失败：\" + (error.message || \"未知错误\"));\n      }\n    },\n    async getElectricityFeeData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityFeeData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              zdf: parseFloat(item.zdf || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity fee data retrieved successfully:\",\n            this.electricityFeeData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity fee data:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity fee data:\", error);\n      }\n    },\n  },\n  async created() {\n    // 初始化建筑信息\n    try {\n      this.curBuilding = this.gf.getCurBuilding();\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.warn('未获取到当前建筑信息，将使用默认建筑ID');\n        // 设置一个默认的建筑ID，或者从其他地方获取\n        this.curBuilding = { id: 1 }; // 可以根据实际情况调整\n      }\n    } catch (error) {\n      console.error('获取建筑信息失败:', error);\n      // 设置默认建筑ID\n      this.curBuilding = { id: 1 };\n    }\n\n    // 初始化获取 token\n    if (!this.checkTokenValidity()) {\n      await this.getToken();\n    }\n\n    // 更新数据\n    await this.updateConsumptionData();\n    await this.updateFeeData();\n    await this.getMeterReadings();\n    await this.getElectricityUsageData();\n    await this.getElectricityFeeData();\n\n    // 每5分钟更新一次 token\n    setInterval(async () => {\n      await this.getToken();\n    }, 5 * 60 * 1000);\n\n    // 每5分钟更新一次数据\n    setInterval(async () => {\n      await this.updateConsumptionData();\n      await this.updateFeeData();\n      await this.getMeterReadings();\n      await this.getElectricityUsageData();\n      await this.getElectricityFeeData();\n    }, 5 * 60 * 1000);\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.all {\n  display: flex;\n  flex-direction: row;\n  margin-top: 5px;\n\n  .zong {\n    display: flex;\n    flex-direction: row;\n    margin-top: 10px;\n    .echart1,\n    .echart2 {\n      flex: 1;\n\n      .center {\n        margin-top: -24px;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: 400;\n        font-size: 17px;\n        color: #00ffb6;\n        text-align: center;\n        margin-bottom: 10px;\n      }\n\n      .btn {\n        width: 133px;\n        height: 31px;\n        border: 1px solid #2d6cb0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: bold;\n        font-size: 15px;\n        color: #ffffff;\n        border-radius: 30px;\n        margin-left: 7%;\n      }\n    }\n  }\n\n  .ltitle1 {\n    margin-top: 10px;\n    position: relative;\n  }\n\n  .line1 {\n    width: 2px;\n    height: 823px;\n    opacity: 0.64;\n    background-color: #204964;\n  }\n\n  .all1 {\n    flex: 557;\n\n    .nenghao {\n      width: 257px;\n      height: 183px;\n      background: url(\"../assets/image/nenghao.png\");\n      background-size: 100% 100%;\n      margin-left: 100px;\n      margin-top: 45px;\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 400;\n      font-size: 20px;\n      color: #ffffff;\n      line-height: 213px;\n    }\n\n    .nhp {\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 500;\n      font-size: 52px;\n      color: #2cc1ff;\n      margin-top: 8px;\n    }\n\n    .nh {\n      margin-left: 24px;\n      margin-top: 32px;\n      width: 423px;\n      height: 105px;\n      border: 1px solid #364d5a;\n      background-size: 100% 100%;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      padding-left: 72px;\n      margin-bottom: 5px;\n      // justify-content: space-evenly;\n\n      .nhimg {\n        width: 107px;\n        height: 90px;\n        margin-right: 35px;\n      }\n\n      .nhtit {\n        width: 148px;\n        margin-left: 10px;\n        margin-top: 3px;\n\n        .p11 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #7acfff;\n        }\n\n        .p12 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #ffa170;\n        }\n\n        .p2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 20px;\n          color: #ffffff;\n        }\n      }\n\n      .nhtit1 {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-left: 35px;\n\n        .nhimg1 {\n          width: 16px;\n          height: 20px;\n        }\n\n        .pp1 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #0df29b;\n        }\n\n        .pp2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #ffa170;\n        }\n      }\n\n      .nht {\n        margin-top: 10px;\n        display: flex;\n        flex-direction: column;\n\n        .pp {\n          margin-left: 35px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n\n          color: #cccccc;\n        }\n      }\n    }\n  }\n\n  .all2 {\n    margin-left: -52px;\n    flex: 627;\n    display: flex;\n    flex-direction: column;\n    .shinei {\n      .itemshei {\n        display: flex;\n        justify-content: space-around;\n        .nenghaos {\n          width: 227px;\n          height: 173px;\n          background: url(\"../assets/image/nenghao.png\");\n          background-size: 100% 100%;\n          text-align: center;\n          margin-left: 10px;\n          margin-top: 33px;\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 14px;\n          color: #ffffff;\n          line-height: 144px;\n        }\n        .nhps {\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 21px;\n          color: #2cc1ff;\n          margin-top: 8px;\n        }\n      }\n    }\n  }\n\n  .all3 {\n    flex: 658;\n    margin-left: 15px;\n  }\n}\n\n.shinei {\n  width: 100%;\n  height: 100%;\n}\n.shuantitle {\n  width: 100%;\n  display: flex;\n  margin-top: 10px;\n  .title {\n    width: 95%;\n    background: url(\"../assets/image/title.png\");\n    background-size: 100% 100%;\n\n    height: 25px;\n    font-family: Source Han Sans SC;\n    font-weight: 400;\n    font-size: 25px;\n    color: #ffffff;\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\n    font-style: italic;\n    text-align: left;\n    line-height: 4px;\n    padding-left: 33px;\n  }\n}\n.nenghao {\n  width: 167px;\n  height: 113px;\n  background: url(\"../assets/image/nenghao.png\");\n  background-size: 100% 100%;\n  text-align: center;\n  margin-left: 83px;\n  // margin-top: 63px;\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 14px;\n  color: #ffffff;\n  line-height: 144px;\n}\n.nhp {\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 500;\n  font-size: 25px;\n  color: #2cc1ff;\n  margin-top: 8px;\n  width: 79%;\n}\n\n.contents {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: url(\"../assets/image/zichanbeijin.png\");\n  width: 1863px;\n  height: 868px;\n  z-index: 99999;\n  padding-left: 34px;\n  padding-right: 22px;\n  padding-top: 21px;\n}\n.toubu {\n  width: 100%;\n\n  position: relative;\n}\n.el-select {\n  margin-top: -1px;\n  margin-left: 10px;\n  background: #00203d;\n  border-radius: 3px;\n  border: 1px solid #3e89db;\n\n  /deep/.el-select__wrapper {\n    background: #00203d !important;\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper .is-hovering:not {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper:hover {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__placeholder.is-transparent {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select__placeholder {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select-dropdown__item.is-hovering {\n    background-color: #2cc1ff !important;\n  }\n}\n.sp {\n  margin-top: -5px;\n  margin-left: 12px;\n  font-family: Alibaba PuHuiTi;\n  font-weight: bold;\n  font-size: 21px;\n  color: #2cc1ff;\n}\n.img1sss {\n  cursor: pointer;\n  width: 15px;\n  height: 15px;\n}\n\n.table-container {\n  cursor: pointer;\n  .el-table {\n    background-color: transparent !important;\n\n    // 设置滚动条样式\n    ::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n    }\n\n    ::-webkit-scrollbar-thumb {\n      background: #0a3054;\n      border-radius: 3px;\n    }\n\n    ::-webkit-scrollbar-track {\n      background: #1e415c;\n      border-radius: 3px;\n    }\n\n    // 设置表格背景透明\n    ::v-deep .el-table__body-wrapper {\n      background-color: transparent;\n\n      &::-webkit-scrollbar {\n        width: 6px;\n        height: 6px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #0a3054;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #1e415c;\n        border-radius: 3px;\n      }\n    }\n  }\n}\n\n.custom-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.custom-modal {\n  width: 1300px;\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  border-radius: 8px;\n  padding: 0;\n  \n  .modal-header {\n    background: #1B2A47;\n    border-bottom: 1px solid #00E4FF;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .modal-title {\n      color: #00E4FF;\n      font-size: 18px;\n      font-weight: bold;\n    }\n\n    .header-buttons {\n      display: flex;\n      align-items: center;\n      \n      .close-text {\n        color: #00E4FF;\n        margin-right: 15px;\n      }\n\n      .close-btn {\n        color: #00E4FF;\n        font-size: 20px;\n        cursor: pointer;\n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n\n  .modal-content {\n    padding: 20px;\n    background: #1B2A47;\n\n    .search-container {\n      margin-bottom: 20px;\n      \n      .el-button--primary {\n        background: #1B2A47;\n        border: 1px solid #00E4FF;\n        color: #00E4FF;\n        \n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n\n    .el-table {\n      background: #1B2A47 !important;\n      border: 1px solid #00E4FF;\n      \n      &::before {\n        display: none;\n      }\n\n      th {\n        background: #162442 !important;\n        border-bottom: 1px solid #00E4FF !important;\n        color: #00E4FF !important;\n        font-weight: bold;\n      }\n\n      td {\n        background: #1B2A47 !important;\n        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;\n        color: #fff !important;\n      }\n\n      .el-table__row:hover > td {\n        background: #243B6B !important;\n      }\n    }\n\n    .el-table--border::after {\n      display: none;\n    }\n  }\n}\n\n// 修改日期选择器样式\n:deep(.el-date-editor) {\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  \n  .el-range-input {\n    background: #1B2A47;\n    color: #fff;\n  }\n  \n  .el-range-separator {\n    color: #00E4FF;\n  }\n}\n\n// 修改滚动条样式\n:deep(.el-table__body-wrapper::-webkit-scrollbar) {\n  width: 6px;\n  height: 6px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {\n  background: #00E4FF;\n  border-radius: 3px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {\n  background: #1B2A47;\n}\n\n.title-container {\n  position: absolute;\n  top: -9px;\n  left: 57.5%;\n  width: 100%;\n  z-index: 1000;\n\n  .more-btn {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    color: #2cc1ff;\n    font-size: 20px;\n\n    &:hover {\n      opacity: 0.8;\n    }\n\n    i {\n      margin-left: 5px;\n    }\n  }\n}\n</style>\n"], "mappings": ";OAwDUA,UAAiC;OAWVC,UAAkC;OAsBlCC,UAAkC;;;EAxF5DC,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAO;;;EAEdC,KAA6D,EAA7D;IAAA;IAAA;IAAA;EAAA;;;EAGKA,KAAuD,EAAvD;IAAA;IAAA;IAAA;EAAA;AAAuD;;EAsDzDD,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAM;;EAGVA,KAAK,EAAC;AAAK;;EACTA,KAAK,EAAC;AAAI;;EAERA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAK;;EAQbA,KAAK,EAAC;AAAI;;EAERA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAK;;EAQbA,KAAK,EAAC;AAAI;;EAERA,KAAK,EAAC;AAAO;;EACbA,KAAK,EAAC;AAAK;;EA2BjBA,KAAK,EAAC;AAAM;;EAQRA,KAAK,EAAC;AAAQ;;EAYlBA,KAAK,EAAC;AAAM;;EAGNA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAiB;;EA8C3BA,KAAK,EAAC;AAAQ;;EACZA,KAAK,EAAC;AAAiB;;EAMvBA,KAAK,EAAC;AAAiB;;EA0D/BA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAElBA,KAAK,EAAC;AAAgB;;EAaxBA,KAAK,EAAC;AAAe;;EAEnBA,KAAK,EAAC;AAAkB;;;;;;;;;;SA9QTE,KAAA,CAAAC,MAAM,I,cAAlCC,mBAAA,CAwWM,OAxWNC,UAwWM,GAvWJC,mBAAA,CAmPM,OAnPNC,UAmPM,GAhPI,KAAK,I,cAFbH,mBAAA,CAwDM,OAxDNI,UAwDM,GApDJF,mBAAA,CA4CM,OA5CNG,UA4CM,G,0BA3CJH,mBAAA,CAA6B;IAAvBN,KAAK,EAAC;EAAI,GAAC,OAAK,sBACtBU,YAAA,CAaYC,oBAAA;IAZVX,KAAK,EAAC,WAAW;gBACRE,KAAA,CAAAU,YAAY;+DAAZV,KAAA,CAAAU,YAAY,GAAAC,MAAA;IACrBC,WAAW,EAAC,cAAc;IAC1Bb,KAAmC,EAAnC;MAAA;MAAA;IAAA,CAAmC;IAClCc,QAAM,EAAEC,IAAA,CAAAC;;sBAGP,MAAwB,E,kBAD1Bb,mBAAA,CAKEc,SAAA,QAAAC,WAAA,CAJejB,KAAA,CAAAkB,QAAQ,EAAhBC,IAAI;2BADbC,YAAA,CAKEC,oBAAA;QAHCC,GAAG,EAAEH,IAAI,CAACI,KAAK;QACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;QACjBD,KAAK,EAAEJ,IAAI,CAACI;;;;iDAGjBf,YAAA,CAaYC,oBAAA;IAZVX,KAAK,EAAC,WAAW;gBACRE,KAAA,CAAAyB,YAAY;+DAAZzB,KAAA,CAAAyB,YAAY,GAAAd,MAAA;IACrBC,WAAW,EAAC,cAAc;IAC1Bb,KAAmC,EAAnC;MAAA;MAAA;IAAA,CAAmC;IAClCc,QAAM,EAAEC,IAAA,CAAAC;;sBAGP,MAAwB,E,kBAD1Bb,mBAAA,CAKEc,SAAA,QAAAC,WAAA,CAJejB,KAAA,CAAA0B,QAAQ,EAAhBP,IAAI;2BADbC,YAAA,CAKEC,oBAAA;QAHCC,GAAG,EAAEH,IAAI,CAACI,KAAK;QACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;QACjBD,KAAK,EAAEJ,IAAI,CAACI;;;;iDAGjBf,YAAA,CAaYC,oBAAA;IAZVX,KAAK,EAAC,WAAW;gBACRE,KAAA,CAAA2B,YAAY;+DAAZ3B,KAAA,CAAA2B,YAAY,GAAAhB,MAAA;IACrBC,WAAW,EAAC,cAAc;IAC1Bb,KAAmC,EAAnC;MAAA;MAAA;IAAA,CAAmC;IAClCc,QAAM,EAAEC,IAAA,CAAAC;;sBAGP,MAAwB,E,kBAD1Bb,mBAAA,CAKEc,SAAA,QAAAC,WAAA,CAJejB,KAAA,CAAA4B,QAAQ,EAAhBT,IAAI;2BADbC,YAAA,CAKEC,oBAAA;QAHCC,GAAG,EAAEH,IAAI,CAACI,KAAK;QACfC,KAAK,EAAEL,IAAI,CAACK,KAAK;QACjBD,KAAK,EAAEJ,IAAI,CAACI;;;;mDAKXvB,KAAA,CAAAC,MAAM,I,cADdC,mBAAA,CAME;;IAJAJ,KAAK,EAAC,SAAS;IACd+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAnB,MAAA,IAAEoB,QAAA,CAAAC,KAAK;IACbC,GAAiC,EAAjCtC,UAAiC;IACjCuC,GAAG,EAAC;kFAIR9B,mBAAA,CAuLM,OAvLN+B,UAuLM,GAtLJ/B,mBAAA,CAsDM,OAtDNgC,UAsDM,GArDJ5B,YAAA,CA2CS6B,iBAAA;IA3CDvC,KAAK,EAAC,QAAQ;IAACwC,GAAG,EAAC;;sBACzB,MAAiC,C,4BAAjClC,mBAAA,CAAiC;MAA5BN,KAAK,EAAC;IAAS,GAAC,QAAM,sBAC3BM,mBAAA,CAA8D,KAA9DmC,UAA8D,EAAAC,gBAAA,CAA5CxC,KAAA,CAAAyC,gBAAgB,CAACC,KAAK,CAACC,OAAO,OAAM,MAAI,iBAC1DvC,mBAAA,CAUM,OAVNwC,UAUM,G,4BATJxC,mBAAA,CAA+D;MAA1DN,KAAK,EAAC,OAAO;MAACmC,GAAkC,EAAlCrC,UAAkC;MAACsC,GAAG,EAAC;iCAC1D9B,mBAAA,CAGM,OAHNyC,UAGM,GAFJzC,mBAAA,CAA6D,KAA7D0C,WAA6D,EAAAN,gBAAA,CAA3CxC,KAAA,CAAAyC,gBAAgB,CAACM,KAAK,CAACJ,OAAO,OAAM,KAAG,iB,4BACzDvC,mBAAA,CAAwB;MAArBN,KAAK,EAAC;IAAI,GAAC,QAAM,qB,GAEtBkD,mBAAA,kRAGU,C,GAEZ5C,mBAAA,CAUM,OAVN6C,WAUM,G,4BATJ7C,mBAAA,CAA+D;MAA1DN,KAAK,EAAC,OAAO;MAACmC,GAAkC,EAXlCrC,UAAkC;MAWCsC,GAAG,EAAC;iCAC1D9B,mBAAA,CAGM,OAHN8C,WAGM,GAFJ9C,mBAAA,CAA8D,KAA9D+C,WAA8D,EAAAX,gBAAA,CAA5CxC,KAAA,CAAAyC,gBAAgB,CAACW,MAAM,CAACT,OAAO,OAAM,KAAG,iB,4BAC1DvC,mBAAA,CAAyB;MAAtBN,KAAK,EAAC;IAAI,GAAC,SAAO,qB,GAEvBkD,mBAAA,qRAGU,C,GAEZ5C,mBAAA,CAiBM,OAjBNiD,WAiBM,G,4BAhBJjD,mBAAA,CAA+D;MAA1DN,KAAK,EAAC,OAAO;MAACmC,GAAkC,EAAlCpC,UAAkC;MAACqC,GAAG,EAAC;iCAC1D9B,mBAAA,CAGM,OAHNkD,WAGM,GAFJlD,mBAAA,CAA+D,KAA/DmD,WAA+D,EAAAf,gBAAA,CAA7CxC,KAAA,CAAAyC,gBAAgB,CAACe,OAAO,CAACb,OAAO,OAAM,KAAG,iB,4BAC3DvC,mBAAA,CAA0B;MAAvBN,KAAK,EAAC;IAAI,GAAC,UAAQ,qB,+BAExBM,mBAAA,CAUM;MAVDN,KAAK,EAAC;IAAK,IACdkD,mBAAA,kXAOU,EACVA,mBAAA,4BAA6B,C;;MAInCA,mBAAA,iYAQa,C,GAEfA,mBAAA,iCAAkC,EAClC5C,mBAAA,CAmBM,OAnBNqD,WAmBM,GAlBJT,mBAAA,yKAIa,EAEbxC,YAAA,CAMS6B,iBAAA;IANDvC,KAAK,EAAC,SAAS;IAACwC,GAAG,EAAC;;sBAC1B,MAIM,CAJNlC,mBAAA,CAIM,OAJNsD,WAIM,GAHJlD,YAAA,CAEgBmD,uBAAA;MADb,kBAAgB,EAAE3D,KAAA,CAAA4D;IAAoB,8C;;MAI7CZ,mBAAA,yMAIa,C,GAEf5C,mBAAA,CAyGM,OAzGNyD,WAyGM,GAxGJzD,mBAAA,CA+CM,cA9CJI,YAAA,CA6CS6B,iBAAA;IA7CDvC,KAAK,EAAC,SAAS;IAACwC,GAAG,EAAC;;sBAC1B,MA2CM,CA3CNlC,mBAAA,CA2CM,OA3CN0D,WA2CM,GA1CJ1D,mBAAA,CAyCM,OAzCN2D,WAyCM,GAxCJvD,YAAA,CAuCWwD,mBAAA;MAtCRC,IAAI,EAAEjE,KAAA,CAAAkE,aAAa;MACpBnE,KAA4C,EAA5C;QAAA;QAAA;MAAA,CAA4C;MAC3C,mBAAiB,EAAE;;;;OAInB;MACA,YAAU,EAAE;;;;OAIZ;MACDoE,MAAM,EAAC;;wBAEP,MAKE,CALF3D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,SAAS;QACd7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;UAER/D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,WAAW;QAChB7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;UAER/D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,WAAW;QAChB7C,KAAK,EAAC,KAAK;QACX8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;UAER/D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,UAAU;QACf7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;;;;;QAOlB/D,YAAA,CAuDS6B,iBAAA;IAvDDvC,KAAK,EAAC,SAAS;IAACC,KAAuB,EAAvB;MAAA;IAAA,CAAuB;IAACuC,GAAG,EAAC;;sBAClD,MAqDM,CArDNlC,mBAAA,CAqDM,OArDNoE,WAqDM,GApDJpE,mBAAA,CAKM,OALNqE,WAKM,GAJJrE,mBAAA,CAGM;MAHDN,KAAK,EAAC,UAAU;MAAE+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,UAAA4C,IAAA,KAAE3C,QAAA,CAAA4C,gBAAA,IAAA5C,QAAA,CAAA4C,gBAAA,IAAAD,IAAA,CAAgB;oCAC5CtE,mBAAA,CAAe,cAAT,IAAE,qBACRA,mBAAA,CAAmC;MAAhCN,KAAK,EAAC;IAAqB,2B,MAGlCM,mBAAA,CA6CM,OA7CNwE,WA6CM,GA5CJpE,YAAA,CA2CWwD,mBAAA;MA1CRC,IAAI,EAAEjE,KAAA,CAAA4D,oBAAoB;MAC3B7D,KAA4C,EAA5C;QAAA;QAAA;MAAA,CAA4C;MAC3C,mBAAiB,EAAE;;;;OAInB;MACA,YAAU,EAAE;;;;OAIZ;MACDoE,MAAM,EAAC;;wBAEP,MAKE,CALF3D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,SAAS;QACd7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;UAER/D,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,WAAW;QAChB7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;UAER/D,YAAA,CASkB4D,0BAAA;QARhBC,IAAI,EAAC,SAAS;QACd7C,KAAK,EAAC,UAAU;QAChB8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;;0BAEN,MAEe,CAFfvB,mBAAA,4IAEe,C;;UAEjBxC,YAAA,CAKE4D,0BAAA;QAJAC,IAAI,EAAC,SAAS;QACd7C,KAAK,EAAC,MAAM;QACZ8C,KAAK,EAAC,QAAQ;QACdC,KAAK,EAAC;;;;;YAUtBvB,mBAAA,WAAc,EAENhD,KAAA,CAAA6E,aAAa,I,cADrB3E,mBAAA,CAgHM;;IA9GJJ,KAAK,EAAC,sBAAsB;IAC3B+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAgD,cAAA,CAAAnE,MAAA,IAAOX,KAAA,CAAA6E,aAAa;MAE1BzE,mBAAA,CA0GM,OA1GN2E,WA0GM,GAzGJ3E,mBAAA,CAcM,OAdN4E,WAcM,G,4BAbJ5E,mBAAA,CAAwC;IAAlCN,KAAK,EAAC;EAAa,GAAC,SAAO,sBACjCM,mBAAA,CAWM,OAXN6E,WAWM,GAVJzE,YAAA,CAKC0E,oBAAA;IAJCC,IAAI,EAAC,MAAM;IACXrF,KAAK,EAAC,YAAY;IACjB+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAnB,MAAA,IAAEX,KAAA,CAAA6E,aAAa;;sBACpB,MAAE/C,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;MAEL1B,mBAAA,CAGK;IAFHN,KAAK,EAAC,yBAAyB;IAC9B+B,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAnB,MAAA,IAAEX,KAAA,CAAA6E,aAAa;UAI3BzE,mBAAA,CAyFM,OAzFNgF,WAyFM,GAxFJpC,mBAAA,UAAa,EACb5C,mBAAA,CAyBM,OAzBNiF,WAyBM,GAxBJ7E,YAAA,CASiB8E,yBAAA;gBARNtF,KAAA,CAAAuF,SAAS;+DAATvF,KAAA,CAAAuF,SAAS,GAAA5E,MAAA;IAClBwE,IAAI,EAAC,WAAW;IAChB,iBAAe,EAAC,GAAG;IACnB,mBAAiB,EAAC,MAAM;IACxB,iBAAe,EAAC,MAAM;IACtB,cAAY,EAAC,YAAY;IACzBpF,KAAwC,EAAxC;MAAA;MAAA;IAAA;2CAGFiD,mBAAA,iWASO,EACPA,mBAAA,uBAA0B,EAE1BxC,YAAA,CAA4D0E,oBAAA;IAAjDC,IAAI,EAAC,SAAS;IAAEtD,OAAK,EAAEE,QAAA,CAAAyD;;sBAAY,MAAE1D,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;kCAChDkB,mBAAA,yEAAwE,C,GAG1EA,mBAAA,YAAe,EACfxC,YAAA,CA0DWwD,mBAAA;IAzDRC,IAAI,EAAEjE,KAAA,CAAAyF,UAAU;IACjB1F,KAAqC,EAArC;MAAA;MAAA;IAAA,CAAqC;IACpC,mBAAiB,EAAE;;;;KAInB;IACA,YAAU,EAAE;;;;KAIZ;IACDoE,MAAM,EAAC;;sBAEP,MAKE,CALF3D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,SAAS;MACd7C,KAAK,EAAC,MAAM;MACZ8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAER/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,WAAW;MAChB7C,KAAK,EAAC,MAAM;MACZ8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAER/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,WAAW;MAChB7C,KAAK,EAAC,IAAI;MACV8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAER/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,SAAS;MACd7C,KAAK,EAAC,IAAI;MACV8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAER/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,SAAS;MACd7C,KAAK,EAAC,UAAU;MAChB8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAER/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,MAAM;MACX7C,KAAK,EAAC,MAAM;MACZ8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC;QAGR/D,YAAA,CAKE4D,0BAAA;MAJAC,IAAI,EAAC,SAAS;MACd7C,KAAK,EAAC,MAAM;MACZ8C,KAAK,EAAC,QAAQ;MACdC,KAAK,EAAC", "ignoreList": []}]}