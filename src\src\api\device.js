import http from "@/utils/request";

// 获取设备曲线图
export function getDeviceData(deviceId, dmId) {
  console.log(deviceId, dmId, 'getDeviceData')
  const today = new Date();

  // Helper function to format date as "YYYY-MM-DD HH:mm:ss"
  const formatDate = (date) => {
    const pad = (num) => String(num).padStart(2, "0");
    return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(
      date.getDate()
    )} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(
      date.getSeconds()
    )}`;
  };

  const startOfDay = new Date(today.setHours(0, 0, 0, 0));
  const endOfDay = new Date(today.setHours(23, 59, 59, 999));

  return http.get("/device/api/deviceItemDataList", {
    deviceId,
    itemDataId: dmId !== undefined ? dmId : `${deviceId}1`, // 修复条件逻辑
    from: formatDate(startOfDay),
    to: formatDate(endOfDay),
  });
}

// export function getDeviceData(deviceId, dmId) {
//   return http.get("/device/api/deviceItemDataList", {
//     deviceId,
//     itemDataId: dmId !== undefined ? dmId : `${deviceId}1`, // 修复条件逻辑
//     // limit: 200,
//     from: "2025-01-22 00:00:00",
//     to: "2025-01-22 23:59:59",
//   });
// }

//获取设备详细信息
export function getDevicedetails(deviceId) {
  return http.get("/device/api/resourceDevice", {
    deviceId,
  });
}

// 获取设备警告列表
export function getDeviceWarningList(params) {
  return http.get("/device/api/deviceWarningList", {
    pageNum: params.pageNum || 1,
    pageSize: params.pageSize || 10,
    buildingId: params.buildingId || 1,
    severitys: params.severitys || "一级,二级,三级",
    hasFixed: params.hasFixed,
    deviceTypes: params.deviceTypes,
    deviceType: params.deviceType
  });
}

// 建筑总用能数据列表
// @ApiImplicitParam(name="buildingId", value="建筑ID", dataType="int", required=true),
// @ApiImplicitParam(name="from", value="时间起始", dataType="string", required=true),
// @ApiImplicitParam(name="to", value="时间终点", dataType="string", required=true),
// @ApiImplicitParam(name="deviceType", value="能耗类型", dataType="string", required=true),
// @ApiImplicitParam(name="displayType", value="显示类型", dataType="string", required=true),
export function buildingEnergyDataList(query) {
  return http.post('/energy/api/buildingEnergyDataList', query);
}
