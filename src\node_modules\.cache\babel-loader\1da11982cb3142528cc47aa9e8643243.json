{"remainingRequest": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue", "mtime": 1751445617508}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\babel.config.js", "mtime": 1726672621424}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["XLSX", "Electricity", "biao1s", "biao1ss", "Titles", "Electricity2", "Electricity3", "Electricity4", "Electricity5", "Electricity6", "Electricity7", "Electricity8", "huanxing", "axios", "buildingEnergyDataList", "components", "data", "isshow", "pickerOptions", "shortcuts", "text", "onClick", "picker", "end", "Date", "start", "setTime", "getTime", "$emit", "options", "value", "label", "selectvalue2", "options2", "selectvalue3", "options3", "selectvalue1", "options1", "options4", "selectvalue4", "optionData", "name", "itemStyle", "color", "opacity", "optionData1", "token", "systemnum", "tokenvalue", "expiretime", "baseURL", "isTokenValid", "totalConsumption", "daily", "weekly", "monthly", "total", "curBuilding", "totalElectricityFee", "roomtag", "electricityFees", "yesterday", "yearly", "meterReadings", "electricityUsageData", "dialogVisible", "date<PERSON><PERSON><PERSON>", "detailData", "electricityFeeData", "methods", "anniu", "buildParams", "type", "id", "console", "error", "param", "buildingId", "deviceType", "displayType", "from", "$moment", "startOf", "format", "to", "subtract", "getEnergyDataByType", "res", "datas", "reduce", "sum", "d", "parseInt", "totalVal", "getToken", "response", "get", "params", "<PERSON><PERSON><PERSON>", "resultvalue", "tokenData", "timestamp", "localStorage", "setItem", "JSON", "stringify", "log", "errmsg", "checkTokenValidity", "storedToken", "getItem", "parse", "expireTime", "currentTime", "tokenTimestamp", "getElectricityUsage", "startTime", "endTime", "post", "starttime", "endtime", "item", "parseFloat", "ylvalue", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "getPaymentStatistics", "totalFee", "zdf", "updateConsumptionData", "updateConsumptionDataFallback", "now", "today", "setDate", "weekAgo", "monthAgo", "updateFeeData", "monthStart", "yearStart", "getMeterReadings", "sevenDaysAgo", "tag", "nodeid", "getElectricityUsageData", "map", "sort", "a", "b", "readtime", "showDetailDialog", "split", "searchData", "length", "$message", "warning", "zhaddress", "startcode", "toFixed", "endcode", "jfmx", "replace", "success", "message", "exportToExcel", "exportData", "房间标识", "住户地址", "起码", "止码", "缴费明细", "抄表时间", "ws", "utils", "json_to_sheet", "wb", "book_new", "book_append_sheet", "wch", "fileName", "writeFile", "getElectricityFeeData", "created", "gf", "getCurBuilding", "warn", "setInterval"], "sources": ["E:\\svn\\SH-20240918-0186-SFTianjinUniversityH5\\src\\src\\views\\nenghao.vue"], "sourcesContent": ["<template>\n  <div class=\"contents\" v-if=\"isshow\">\n    <div class=\"toubu\">\n      <div\n        style=\"margin-left: 20px; display: flex; align-items: center\"\n        v-if=\"false\"\n      >\n        <div style=\"display: flex; width: 100%; align-items: center\">\n          <span class=\"sp\">当前位置：</span>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue1\"\n            placeholder=\"selectvalue1\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options1\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue2\"\n            placeholder=\"selectvalue2\"\n            style=\"width: 64px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options2\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n          <el-select\n            class=\"el-select\"\n            v-model=\"selectvalue3\"\n            placeholder=\"selectvalue3\"\n            style=\"width: 78px; height: 35.1px\"\n            @change=\"handleChange\"\n          >\n            <el-option\n              v-for=\"item in options3\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            />\n          </el-select>\n        </div>\n        <img\n          v-if=\"isshow\"\n          class=\"img1sss\"\n          @click=\"anniu()\"\n          src=\"../assets/image/table-x.png\"\n          alt=\"\"\n        />\n      </div>\n\n      <div class=\"all\">\n        <div class=\"all1\">\n          <Titles class=\"ltitle\" tit=\"大型仪器平台概况\">\n            <div class=\"nenghao\">累计总能耗:</div>\n            <p class=\"nhp\">{{ totalConsumption.total.toFixed(1) }} kwh</p>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.daily.toFixed(1) }}kwh</p>\n                <p class=\"p2\">本日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.daily - totalConsumption.daily) / totalConsumption.daily * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao1.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p11\">{{ totalConsumption.weekly.toFixed(1) }}kwh</p>  \n                <p class=\"p2\">近7日累计能耗</p>\n              </div>\n              <!-- <div class=\"nhtit1\">\n                <img class=\"nhimg1\" src=\"../assets/image/nhxia.png\" alt=\"\" />\n                <p class=\"pp1\">{{ ((totalConsumption.weekly - totalConsumption.weekly) / totalConsumption.weekly * 100).toFixed(1) }}%</p>\n              </div> -->\n            </div>\n            <div class=\"nh\">\n              <img class=\"nhimg\" src=\"../assets/image/nenghao2.png\" alt=\"\" />\n              <div class=\"nhtit\">\n                <p class=\"p12\">{{ totalConsumption.monthly.toFixed(1) }}kwh</p>\n                <p class=\"p2\">近30日累计能耗</p>\n              </div>\n              <div class=\"nht\">\n                <!-- <div class=\"nhtit1\">\n                  <img\n                    class=\"nhimg1\"\n                    src=\"../assets/image/nhshang.png\"\n                    alt=\"\"\n                  />\n                  <p class=\"pp2\">{{ ((totalConsumption.monthly - totalConsumption.monthly) / totalConsumption.monthly * 100).toFixed(1) }}%</p>\n                </div> -->\n                <!-- <p class=\"pp\">环比</p> -->\n              </div>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle\" style=\"margin-top: 20px\" tit=\"电耗费用\">\n            <div class=\"shinei\">\n              <Electricity2\n                :yesterday-fee=\"electricityFees.yesterday\"\n                :monthly-fee=\"electricityFees.monthly\"\n                :yearly-fee=\"electricityFees.yearly\"\n              ></Electricity2>\n            </div>\n          </Titles> -->\n        </div>\n        <!-- <div class=\"line1\"></div> -->\n        <div class=\"all2\">\n          <!-- <Titles class=\"ltitle1\" tit=\"峰平谷用电量\">\n            <div class=\"shinei\">\n              <Electricity8></Electricity8>\n            </div>\n          </Titles> -->\n\n          <Titles class=\"ltitle1\" tit=\"用电量排名\">\n            <div class=\"shinei\">\n              <Electricity3\n                :electricity-data=\"electricityUsageData\"\n              ></Electricity3>\n            </div>\n          </Titles>\n          <!-- <Titles class=\"ltitle1\" tit=\"分区用电量\">\n            <div class=\"shinei\">\n              <Electricity4 :fee-data=\"electricityFeeData\"></Electricity4>\n            </div>\n          </Titles> -->\n        </div>\n        <div class=\"all3\">\n          <div>\n            <Titles class=\"ltitle1\" tit=\"抄电表记录\">\n              <div class=\"shinei\">\n                <div class=\"table-container\">\n                  <el-table\n                    :data=\"meterReadings\"\n                    style=\"width: 100%; background: transparent\"\n                    :header-cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    :cell-style=\"{\n                      background: '#2A363F',\n                      color: '#fff',\n                      borderColor: '#1e415c',\n                    }\"\n                    height=\"320\"\n                  >\n                    <el-table-column\n                      prop=\"roomtag\"\n                      label=\"房间标识\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"zhaddress\"\n                      label=\"住户地址\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                    <el-table-column\n                      prop=\"readvalue\"\n                      label=\"抄表值\"\n                      align=\"center\"\n                      width=\"120\"\n                    />\n                    <el-table-column\n                      prop=\"readtime\"\n                      label=\"抄表时间\"\n                      align=\"center\"\n                      width=\"180\"\n                    />\n                  </el-table>\n                </div>\n              </div>\n            </Titles>\n          </div>\n          <Titles class=\"ltitle1\" style=\"margin-top:15px\" tit=\"用电量记录\">\n            <div class=\"shinei\">\n              <div class=\"title-container\">\n                <div class=\"more-btn\" @click=\"showDetailDialog\">\n                  <span>更多</span>\n                  <i class=\"el-icon-arrow-right\"></i>\n                </div>\n              </div>\n              <div class=\"table-container\">\n                <el-table\n                  :data=\"electricityUsageData\"\n                  style=\"width: 100%; background: transparent\"\n                  :header-cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#38444C',\n                  }\"\n                  :cell-style=\"{\n                    background: '#2A363F',\n                    color: '#fff',\n                    borderColor: '#1e415c',\n                  }\"\n                  height=\"400\"\n                >\n                  <el-table-column\n                    prop=\"roomtag\"\n                    label=\"房间标识\"\n                    align=\"center\"\n                    width=\"120\"\n                  />\n                  <el-table-column\n                    prop=\"zhaddress\"\n                    label=\"住户地址\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                  <el-table-column\n                    prop=\"ylvalue\"\n                    label=\"用电量(kwh)\"\n                    align=\"center\"\n                    width=\"120\"\n                  >\n                    <!-- <template slot-scope=\"scope\">\n                      {{ parseFloat(scope.row.ylvalue).toFixed(2) }}\n                    </template> -->\n                  </el-table-column>\n                  <el-table-column\n                    prop=\"endtime\"\n                    label=\"抄表时间\"\n                    align=\"center\"\n                    width=\"180\"\n                  />\n                </el-table>\n              </div>\n            </div>\n          </Titles>\n        </div>\n      </div>\n    </div>\n\n    <!-- 自定义弹窗 -->\n    <div\n      v-if=\"dialogVisible\"\n      class=\"custom-modal-overlay\"\n      @click.self=\"dialogVisible = false\"\n    >\n      <div class=\"custom-modal\">\n        <div class=\"modal-header\">\n          <span class=\"modal-title\">用电量详细记录</span>\n          <div class=\"header-buttons\">\n            <el-button\n              type=\"text\"\n              class=\"close-text\"\n              @click=\"dialogVisible = false\"\n              >关闭</el-button\n            >\n            <i\n              class=\"el-icon-close close-btn\"\n              @click=\"dialogVisible = false\"\n            ></i>\n          </div>\n        </div>\n        <div class=\"modal-content\">\n          <!-- 搜索条件 -->\n          <div class=\"search-container\">\n            <el-date-picker\n              v-model=\"dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              value-format=\"YYYY-MM-DD\"\n              style=\"width: 380px; margin-right: 15px\"\n            >\n            </el-date-picker>\n            <!-- <el-date-picker\n                v-model=\"dateRange\"\n                type=\"daterange\"\n                align=\"right\"\n                unlink-panels\n                range-separator=\"至\"\n                start-placeholder=\"开始日期\"\n                end-placeholder=\"结束日期\"\n                :picker-options=\"pickerOptions\"\n              > -->\n            <!-- </el-date-picker> -->\n\n            <el-button type=\"primary\" @click=\"searchData\">查询</el-button>\n            <!-- <el-button type=\"success\" @click=\"exportToExcel\">导出</el-button> -->\n          </div>\n\n          <!-- 详细数据表格 -->\n          <el-table\n            :data=\"detailData\"\n            style=\"width: 100%; margin-top: 20px\"\n            :header-cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            :cell-style=\"{\n              background: '#2C4255',\n              color: '#fff',\n              borderColor: '#1e415c',\n            }\"\n            height=\"500\"\n          >\n            <el-table-column\n              prop=\"roomtag\"\n              label=\"房间标识\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"zhaddress\"\n              label=\"住户地址\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"startcode\"\n              label=\"起码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"endcode\"\n              label=\"止码\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"ylvalue\"\n              label=\"用电量(kwh)\"\n              align=\"center\"\n              width=\"180\"\n            />\n            <el-table-column\n              prop=\"jfmx\"\n              label=\"缴费明细\"\n              align=\"center\"\n              width=\"180\"\n            />\n\n            <el-table-column\n              prop=\"endtime\"\n              label=\"抄表时间\"\n              align=\"center\"\n              width=\"181\"\n            />\n          </el-table>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as XLSX from \"xlsx\";\nimport Electricity from \"../components/echarts/dianbiao/biao1.vue\";\nimport biao1s from \"../components/echarts/dianbiao/biao1s.vue\";\nimport biao1ss from \"../components/echarts/dianbiao/biao1ss.vue\";\nimport Titles from \"../components/common/Titles.vue\";\n\nimport Electricity2 from \"../components/echarts/dianbiao/Electricity2.vue\";\nimport Electricity3 from \"../components/echarts/dianbiao/Electricity3.vue\";\nimport Electricity4 from \"../components/echarts/dianbiao/Electricity4.vue\";\nimport Electricity5 from \"../components/echarts/dianbiao/Electricity5.vue\";\nimport Electricity6 from \"../components/echarts/dianbiao/Electricity6.vue\";\nimport Electricity7 from \"../components/echarts/dianbiao/Electricity7.vue\";\nimport Electricity8 from \"../components/echarts/dianbiao/Electricity8.vue\";\nimport huanxing from \"@/components/echarts/xiaobingtu.vue\";\nimport axios from \"axios\";\nimport { buildingEnergyDataList } from \"@/api/device\";\n\nexport default {\n  components: {\n    Titles,\n    Electricity,\n    Electricity2,\n    Electricity3,\n    Electricity4,\n    Electricity5,\n    Electricity6,\n    Electricity7,\n    Electricity8,\n    huanxing,\n    biao1s,\n    biao1ss,\n  },\n  data() {\n    return {\n      isshow: true,\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: \"最近一周\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近一个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n          {\n            text: \"最近三个月\",\n            onClick(picker) {\n              const end = new Date();\n              const start = new Date();\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);\n              picker.$emit(\"pick\", [start, end]);\n            },\n          },\n        ],\n      },\n      options: [\n        {\n          value: \"总览\",\n          label: \"总览\",\n        },\n        {\n          value: \"能耗分析\",\n          label: \"能耗分析\",\n        },\n        {\n          value: \"能流分析\",\n          label: \"能流分析\",\n        },\n        {\n          value: \"设备状态\",\n          label: \"设备状态\",\n        },\n        {\n          value: \"一键抄表\",\n          label: \"一键抄表\",\n        },\n        {\n          value: \"费用管理\",\n          label: \"费用管理\",\n        },\n        {\n          value: \"碳排放管理\",\n          label: \"碳排放管理\",\n        },\n      ],\n      selectvalue2: \"B3\",\n      options2: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B3\",\n      options3: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue1: \"B3\",\n      options1: [\n        {\n          value: \"B3\",\n          label: \"B3\",\n        },\n      ],\n      selectvalue3: \"B1栋\",\n      options4: [\n        {\n          value: \"B1栋\",\n          label: \"B1栋\",\n        },\n        {\n          value: \"B2栋\",\n          label: \"B2栋\",\n        },\n        {\n          value: \"B3栋\",\n          label: \"B3栋\",\n        },\n        {\n          value: \"B4栋\",\n          label: \"B4栋\",\n        },\n        {\n          value: \"W1栋\",\n          label: \"W1栋\",\n        },\n        {\n          value: \"W2栋\",\n          label: \"W2栋\",\n        },\n      ],\n      selectvalue4: \"B1栋\",\n      optionData: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      optionData1: [\n        {\n          name: \"一级告警\",\n          value: 16,\n          itemStyle: { color: \"#EB6877\", opacity: 0.9 },\n        },\n        {\n          name: \"二级告警\",\n          value: 27,\n          itemStyle: { color: \"#F8B551\", opacity: 0.9 },\n        },\n        {\n          name: \"三级告警\",\n          value: 17,\n          itemStyle: { color: \"#B954E8\", opacity: 0.9 },\n        },\n        {\n          name: \"四级告警\",\n          value: 40,\n          itemStyle: { color: \"#0284F0\", opacity: 0.9 },\n        },\n      ],\n      token: {\n        systemnum: \"\",\n        tokenvalue: \"\",\n        expiretime: \"\",\n      },\n      baseURL: \"/power\", // Replace with actual server address\n      // baseURL: 'http://*************:8080',\n      isTokenValid: false,\n      totalConsumption: {\n        daily: 0,\n        weekly: 0,\n        monthly: 0,\n        total: 0, // 累计用量\n      },\n      curBuilding: null, // 当前建筑信息\n      totalElectricityFee: 0,\n      roomtag: \"\", // Add your roomtag here if you want to query specific user\n      electricityFees: {\n        yesterday: 0,\n        monthly: 0,\n        yearly: 0,\n      },\n      meterReadings: [], // 新增抄表记录数据\n      electricityUsageData: [], // 新增用电量数据\n      \n      dialogVisible: false, // 修改为 false，默认关闭\n      dateRange: \"\",\n      detailData: [],\n      electricityFeeData: [], // 新增电费数据\n    };\n  },\n  methods: {\n    anniu() {\n      this.isshow = false;\n    },\n\n    // 构建API参数 - 参考 BimEnergyOverview.vue 的实现\n    buildParams(type) {\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.error('curBuilding 未初始化');\n        return null;\n      }\n\n      const param = {\n        buildingId: this.curBuilding.id,\n        deviceType: 'electricity',\n        type: 'electricity',\n        displayType: \"day\",\n        from: this.$moment().startOf(\"day\").format(\"YYYY-MM-DD\"),\n        to: this.$moment().format(\"YYYY-MM-DD\"),\n      };\n\n      // 根据类型调整参数\n      switch (type) {\n        case 'daily':\n          // 本日用量：昨天到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(1, 'day').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'weekly':\n          // 近7日用量：7天前到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(7, 'days').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'monthly':\n          // 近30日用量：30天前到今天\n          param.displayType = 'day';\n          param.from = this.$moment().subtract(30, 'days').format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n        case 'total':\n          // 累计用量：从很久以前到今天\n          param.displayType = 'total';\n          param.from = this.$moment().subtract(10, 'years').startOf(\"year\").format(\"YYYY-MM-DD\");\n          param.to = this.$moment().format(\"YYYY-MM-DD\");\n          break;\n      }\n\n      return param;\n    },\n\n    // 使用新API获取能耗数据\n    async getEnergyDataByType(type) {\n      const param = this.buildParams(type);\n      if (!param) {\n        return 0;\n      }\n\n      try {\n        const res = await buildingEnergyDataList(param);\n        let total = 0;\n        if (res.data && res.data.datas) {\n          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);\n        }\n        return total;\n      } catch (error) {\n        console.error(`获取${type}数据失败:`, error);\n        return 0;\n      }\n    },\n    async getToken() {\n      try {\n        const response = await axios.get(\n          `${this.baseURL}/api/ztwyPower/getToken`,\n          {\n            params: {\n              systemnum: \"346E473FD1EF46E3A2EE43F393BCAF7C\",\n            },\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const { systemnum, tokenvalue, expiretime } =\n            response.data.resultvalue;\n          this.token = {\n            systemnum,\n            tokenvalue,\n            expiretime,\n          };\n          this.isTokenValid = true;\n\n          // 存储 token 和获取时间\n          const tokenData = {\n            ...this.token,\n            timestamp: new Date().getTime(),\n          };\n          localStorage.setItem(\"powerToken\", JSON.stringify(tokenData));\n\n          console.log(\"Token updated successfully:\", this.token);\n        } else {\n          console.error(\"Failed to get token:\", response.data.errmsg);\n          this.isTokenValid = false;\n        }\n      } catch (error) {\n        console.error(\"Error getting token:\", error);\n        this.isTokenValid = false;\n      }\n    },\n    checkTokenValidity() {\n      const storedToken = localStorage.getItem(\"powerToken\");\n      if (storedToken) {\n        const tokenData = JSON.parse(storedToken);\n        const expireTime = new Date(tokenData.expiretime).getTime();\n        const currentTime = new Date().getTime();\n        const tokenTimestamp = tokenData.timestamp;\n\n        // 检查 token 是否过期或距离上次获取是否超过5分钟\n        if (\n          currentTime < expireTime &&\n          currentTime - tokenTimestamp < 5 * 60 * 1000\n        ) {\n          this.token = {\n            systemnum: tokenData.systemnum,\n            tokenvalue: tokenData.tokenvalue,\n            expiretime: tokenData.expiretime,\n          };\n          this.isTokenValid = true;\n          return true;\n        }\n      }\n      return false;\n    },\n    async getElectricityUsage(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag, // Optional: if empty, will return all users' data\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          // Calculate total consumption by summing ylvalue\n          const totalConsumption = response.data.resultvalue.reduce(\n            (sum, item) => {\n              return sum + parseFloat(item.ylvalue || 0);\n            },\n            0\n          );\n          return totalConsumption;\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n        return 0;\n      }\n    },\n    formatDate(date) {\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, \"0\");\n      const day = String(date.getDate()).padStart(2, \"0\");\n      const hours = String(date.getHours()).padStart(2, \"0\");\n      const minutes = String(date.getMinutes()).padStart(2, \"0\");\n      const seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    },\n    async getPaymentStatistics(startTime, endTime) {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: startTime,\n            endtime: endTime,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          const totalFee = response.data.resultvalue.reduce((sum, item) => {\n            return sum + parseFloat(item.zdf || 0);\n          }, 0);\n          return totalFee;\n        } else {\n          console.error(\n            \"Failed to get payment statistics:\",\n            response.data.errmsg\n          );\n          return 0;\n        }\n      } catch (error) {\n        console.error(\"Error getting payment statistics:\", error);\n        return 0;\n      }\n    },\n    async updateConsumptionData() {\n      console.log(5685);\n      \n      // 检查 curBuilding 是否已初始化\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.error('curBuilding 未初始化，无法获取能耗数据');\n        return;\n      }\n\n      try {\n        // 使用新的API获取四个数据项\n        // 累计用量\n        this.totalConsumption.total = await this.getEnergyDataByType('total');\n\n        // 本日用量\n        this.totalConsumption.daily = await this.getEnergyDataByType('daily');\n\n        // 近7日用量\n        this.totalConsumption.weekly = await this.getEnergyDataByType('weekly');\n\n        // 近30日用量\n        this.totalConsumption.monthly = await this.getEnergyDataByType('monthly');\n\n        console.log('能耗数据更新成功:', this.totalConsumption);\n      } catch (error) {\n        console.error('更新能耗数据失败:', error);\n        // 如果新API失败，回退到原来的方法\n        await this.updateConsumptionDataFallback();\n      }\n    },\n\n    // 原来的数据获取方法作为备用\n    async updateConsumptionDataFallback() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const weekAgo = new Date(today);\n      weekAgo.setDate(weekAgo.getDate() - 7);\n      const monthAgo = new Date(today);\n      monthAgo.setDate(monthAgo.getDate() - 30);\n\n      // Get daily consumption\n      this.totalConsumption.daily = await this.getElectricityUsage(\n        this.formatDate(yesterday),\n        this.formatDate(now)\n      );\n\n      // Get weekly consumption\n      this.totalConsumption.weekly = await this.getElectricityUsage(\n        this.formatDate(weekAgo),\n        this.formatDate(now)\n      );\n\n      // Get monthly consumption\n      this.totalConsumption.monthly = await this.getElectricityUsage(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n\n      // 累计用量使用近30日数据作为近似值\n      this.totalConsumption.total = this.totalConsumption.monthly;\n\n      // Get total electricity fee\n      this.totalElectricityFee = await this.getPaymentStatistics(\n        this.formatDate(monthAgo),\n        this.formatDate(now)\n      );\n    },\n    async updateFeeData() {\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);\n      const yearStart = new Date(today.getFullYear(), 0, 1);\n\n      // Get yesterday's fee\n      this.electricityFees.yesterday = await this.getPaymentStatistics(\n        this.formatDate(yesterday),\n        this.formatDate(today)\n      );\n\n      // Get monthly fee\n      this.electricityFees.monthly = await this.getPaymentStatistics(\n        this.formatDate(monthStart),\n        this.formatDate(now)\n      );\n\n      // Get yearly fee\n      this.electricityFees.yearly = await this.getPaymentStatistics(\n        this.formatDate(yearStart),\n        this.formatDate(now)\n      );\n    },\n    async getMeterReadings() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getAllDbValue`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            tag: 0, // 返回最后一次抄表记录\n            nodeid: 0, // 默认为0，返回全部\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.meterReadings = response.data.resultvalue;\n          console.log(\n            \"Meter readings retrieved successfully:\",\n            this.meterReadings\n          );\n        } else {\n          console.error(\"Failed to get meter readings:\", response.data.errmsg);\n        }\n      } catch (error) {\n        console.error(\"Error getting meter readings:\", error);\n      }\n    },\n    async getElectricityUsageData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityUsageData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              ylvalue: parseFloat(item.ylvalue || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity usage data retrieved successfully:\",\n            this.electricityUsageData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity usage:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity usage:\", error);\n      }\n    },\n    showDetailDialog() {\n      this.dialogVisible = true;\n      this.detailData = []; // 清空数据\n      // 默认显示最近一天的数据\n      const end = new Date();\n      const start = new Date();\n      start.setTime(start.getTime() - 3600 * 1000 * 24); // 最近一天\n      this.dateRange = [\n        this.formatDate(start).split(\" \")[0],\n        this.formatDate(end).split(\" \")[0],\n      ];\n      this.searchData(); // 自动查询最近一天的数据\n    },\n    async searchData() {\n      if (!this.dateRange || this.dateRange.length !== 2) {\n        this.$message.warning(\"请选择日期范围\");\n        return;\n      }\n\n      try {\n        if (!this.isTokenValid) {\n          await this.getToken();\n        }\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getYdlByTime`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: `${this.dateRange[0]} 00:00:00`,\n            endtime: `${this.dateRange[1]} 23:59:59`,\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          if (\n            response.data.resultvalue &&\n            response.data.resultvalue.length > 0\n          ) {\n            this.detailData = response.data.resultvalue\n              .map((item) => ({\n                roomtag: item.roomtag || \"\",\n                zhaddress: item.zhaddress || \"\",\n                startcode: item.startcode\n                  ? parseFloat(item.startcode).toFixed(1)\n                  : \"0.0\",\n                endcode: item.endcode\n                  ? parseFloat(item.endcode).toFixed(1)\n                  : \"0.0\",\n                ylvalue: item.ylvalue\n                  ? parseFloat(item.ylvalue).toFixed(2)\n                  : \"0.00\",\n                jfmx: item.jfmx || \"\",\n                endtime: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n              }))\n              .sort((a, b) => new Date(b.endtime) - new Date(a.endtime));\n            this.$message.success(\"查询成功\");\n          } else {\n            this.detailData = [];\n            this.$message.warning(\"所选时间范围内无数据\");\n          }\n        } else {\n          this.$message.error(\"获取数据失败：\" + response.data.errmsg);\n          this.detailData = [];\n        }\n      } catch (error) {\n        this.$message.error(\"获取数据失败：\" + (error.message || \"未知错误\"));\n        this.detailData = [];\n      }\n    },\n    exportToExcel() {\n      if (!this.detailData || !this.detailData.length) {\n        this.$message.warning(\"暂无数据可导出\");\n        return;\n      }\n\n      try {\n        // 准备要导出的数据\n        const exportData = this.detailData.map((item) => ({\n          房间标识: item.roomtag || \"\",\n          住户地址: item.zhaddress || \"\",\n          起码: item.startcode ? parseFloat(item.startcode).toFixed(1) : \"0.0\",\n          止码: item.endcode ? parseFloat(item.endcode).toFixed(1) : \"0.0\",\n          \"用电量(kwh)\": item.ylvalue\n            ? parseFloat(item.ylvalue).toFixed(2)\n            : \"0.00\",\n          缴费明细: item.jfmx || \"\",\n          抄表时间: item.endtime ? item.endtime.replace(/-/g, \"/\") : \"\",\n        }));\n\n        // 创建工作簿并设置数据\n        const ws = XLSX.utils.json_to_sheet(exportData);\n        const wb = XLSX.utils.book_new();\n        XLSX.utils.book_append_sheet(wb, ws, \"用电量记录\");\n\n        // 设置列宽\n        ws[\"!cols\"] = [\n          { wch: 15 }, // 房间标识\n          { wch: 20 }, // 住户地址\n          { wch: 12 }, // 起码\n          { wch: 12 }, // 止码\n          { wch: 15 }, // 用电量\n          { wch: 15 }, // 缴费明细\n          { wch: 20 }, // 抄表时间\n        ];\n\n        // 直接使用 XLSX.writeFile 导出文件\n        const fileName = `用电量记录_${this.dateRange[0]}_${this.dateRange[1]}.xlsx`;\n        XLSX.writeFile(wb, fileName);\n\n        this.$message.success(\"导出成功\");\n      } catch (error) {\n        console.error(\"Export error:\", error);\n        this.$message.error(\"导出失败：\" + (error.message || \"未知错误\"));\n      }\n    },\n    async getElectricityFeeData() {\n      if (!this.isTokenValid) {\n        await this.getToken();\n      }\n\n      try {\n        const now = new Date();\n        const sevenDaysAgo = new Date(now);\n        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);\n\n        const response = await axios.post(\n          `${this.baseURL}/api/ztwyPower/getJfAllInfos`,\n          {\n            tokenvalue: this.token.tokenvalue,\n            starttime: this.formatDate(sevenDaysAgo),\n            endtime: this.formatDate(now),\n            roomtag: this.roomtag,\n          }\n        );\n\n        if (response.data.errcode === 0) {\n          this.electricityFeeData = response.data.resultvalue\n            .map((item) => ({\n              ...item,\n              zdf: parseFloat(item.zdf || 0),\n            }))\n            .sort((a, b) => new Date(a.readtime) - new Date(b.readtime));\n          console.log(\n            \"Electricity fee data retrieved successfully:\",\n            this.electricityFeeData\n          );\n        } else {\n          console.error(\n            \"Failed to get electricity fee data:\",\n            response.data.errmsg\n          );\n        }\n      } catch (error) {\n        console.error(\"Error getting electricity fee data:\", error);\n      }\n    },\n  },\n  async created() {\n    // 初始化建筑信息\n    try {\n      this.curBuilding = this.gf.getCurBuilding();\n      if (!this.curBuilding || !this.curBuilding.id) {\n        console.warn('未获取到当前建筑信息，将使用默认建筑ID');\n        // 设置一个默认的建筑ID，或者从其他地方获取\n        this.curBuilding = { id: 1 }; // 可以根据实际情况调整\n      }\n    } catch (error) {\n      console.error('获取建筑信息失败:', error);\n      // 设置默认建筑ID\n      this.curBuilding = { id: 1 };\n    }\n\n    // 初始化获取 token\n    if (!this.checkTokenValidity()) {\n      await this.getToken();\n    }\n\n    // 更新数据\n    await this.updateConsumptionData();\n    await this.updateFeeData();\n    await this.getMeterReadings();\n    await this.getElectricityUsageData();\n    await this.getElectricityFeeData();\n\n    // 每5分钟更新一次 token\n    setInterval(async () => {\n      await this.getToken();\n    }, 5 * 60 * 1000);\n\n    // 每5分钟更新一次数据\n    setInterval(async () => {\n      await this.updateConsumptionData();\n      await this.updateFeeData();\n      await this.getMeterReadings();\n      await this.getElectricityUsageData();\n      await this.getElectricityFeeData();\n    }, 5 * 60 * 1000);\n  },\n};\n</script>\n\n<style lang=\"less\" scoped>\n.all {\n  display: flex;\n  flex-direction: row;\n  margin-top: 5px;\n\n  .zong {\n    display: flex;\n    flex-direction: row;\n    margin-top: 10px;\n    .echart1,\n    .echart2 {\n      flex: 1;\n\n      .center {\n        margin-top: -24px;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: 400;\n        font-size: 17px;\n        color: #00ffb6;\n        text-align: center;\n        margin-bottom: 10px;\n      }\n\n      .btn {\n        width: 133px;\n        height: 31px;\n        border: 1px solid #2d6cb0;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-family: \"Source Han Sans SC\", sans-serif;\n        font-weight: bold;\n        font-size: 15px;\n        color: #ffffff;\n        border-radius: 30px;\n        margin-left: 7%;\n      }\n    }\n  }\n\n  .ltitle1 {\n    margin-top: 10px;\n    position: relative;\n  }\n\n  .line1 {\n    width: 2px;\n    height: 823px;\n    opacity: 0.64;\n    background-color: #204964;\n  }\n\n  .all1 {\n    flex: 557;\n\n    .nenghao {\n      width: 257px;\n      height: 183px;\n      background: url(\"../assets/image/nenghao.png\");\n      background-size: 100% 100%;\n      margin-left: 100px;\n      margin-top: 45px;\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 400;\n      font-size: 20px;\n      color: #ffffff;\n      line-height: 213px;\n    }\n\n    .nhp {\n      text-align: center;\n      font-family: Alibaba PuHuiTi;\n      font-weight: 500;\n      font-size: 52px;\n      color: #2cc1ff;\n      margin-top: 8px;\n    }\n\n    .nh {\n      margin-left: 24px;\n      margin-top: 32px;\n      width: 423px;\n      height: 105px;\n      border: 1px solid #364d5a;\n      background-size: 100% 100%;\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      padding-left: 72px;\n      margin-bottom: 5px;\n      // justify-content: space-evenly;\n\n      .nhimg {\n        width: 107px;\n        height: 90px;\n        margin-right: 35px;\n      }\n\n      .nhtit {\n        width: 148px;\n        margin-left: 10px;\n        margin-top: 3px;\n\n        .p11 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #7acfff;\n        }\n\n        .p12 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 30px;\n          color: #ffa170;\n        }\n\n        .p2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 20px;\n          color: #ffffff;\n        }\n      }\n\n      .nhtit1 {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        margin-left: 35px;\n\n        .nhimg1 {\n          width: 16px;\n          height: 20px;\n        }\n\n        .pp1 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #0df29b;\n        }\n\n        .pp2 {\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n          color: #ffa170;\n        }\n      }\n\n      .nht {\n        margin-top: 10px;\n        display: flex;\n        flex-direction: column;\n\n        .pp {\n          margin-left: 35px;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 17px;\n\n          color: #cccccc;\n        }\n      }\n    }\n  }\n\n  .all2 {\n    margin-left: -52px;\n    flex: 627;\n    display: flex;\n    flex-direction: column;\n    .shinei {\n      .itemshei {\n        display: flex;\n        justify-content: space-around;\n        .nenghaos {\n          width: 227px;\n          height: 173px;\n          background: url(\"../assets/image/nenghao.png\");\n          background-size: 100% 100%;\n          text-align: center;\n          margin-left: 10px;\n          margin-top: 33px;\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 400;\n          font-size: 14px;\n          color: #ffffff;\n          line-height: 144px;\n        }\n        .nhps {\n          text-align: center;\n          font-family: Alibaba PuHuiTi;\n          font-weight: 500;\n          font-size: 21px;\n          color: #2cc1ff;\n          margin-top: 8px;\n        }\n      }\n    }\n  }\n\n  .all3 {\n    flex: 658;\n    margin-left: 15px;\n  }\n}\n\n.shinei {\n  width: 100%;\n  height: 100%;\n}\n.shuantitle {\n  width: 100%;\n  display: flex;\n  margin-top: 10px;\n  .title {\n    width: 95%;\n    background: url(\"../assets/image/title.png\");\n    background-size: 100% 100%;\n\n    height: 25px;\n    font-family: Source Han Sans SC;\n    font-weight: 400;\n    font-size: 25px;\n    color: #ffffff;\n    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.47);\n    font-style: italic;\n    text-align: left;\n    line-height: 4px;\n    padding-left: 33px;\n  }\n}\n.nenghao {\n  width: 167px;\n  height: 113px;\n  background: url(\"../assets/image/nenghao.png\");\n  background-size: 100% 100%;\n  text-align: center;\n  margin-left: 83px;\n  // margin-top: 63px;\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 400;\n  font-size: 14px;\n  color: #ffffff;\n  line-height: 144px;\n}\n.nhp {\n  text-align: center;\n  font-family: Alibaba PuHuiTi;\n  font-weight: 500;\n  font-size: 25px;\n  color: #2cc1ff;\n  margin-top: 8px;\n  width: 79%;\n}\n\n.contents {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  background: url(\"../assets/image/zichanbeijin.png\");\n  width: 1863px;\n  height: 868px;\n  z-index: 99999;\n  padding-left: 34px;\n  padding-right: 22px;\n  padding-top: 21px;\n}\n.toubu {\n  width: 100%;\n\n  position: relative;\n}\n.el-select {\n  margin-top: -1px;\n  margin-left: 10px;\n  background: #00203d;\n  border-radius: 3px;\n  border: 1px solid #3e89db;\n\n  /deep/.el-select__wrapper {\n    background: #00203d !important;\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper .is-hovering:not {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__wrapper:hover {\n    box-shadow: none;\n  }\n\n  /deep/.el-select__placeholder.is-transparent {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select__placeholder {\n    color: #2cc1ff;\n  }\n\n  /deep/.el-select-dropdown__item.is-hovering {\n    background-color: #2cc1ff !important;\n  }\n}\n.sp {\n  margin-top: -5px;\n  margin-left: 12px;\n  font-family: Alibaba PuHuiTi;\n  font-weight: bold;\n  font-size: 21px;\n  color: #2cc1ff;\n}\n.img1sss {\n  cursor: pointer;\n  width: 15px;\n  height: 15px;\n}\n\n.table-container {\n  cursor: pointer;\n  .el-table {\n    background-color: transparent !important;\n\n    // 设置滚动条样式\n    ::-webkit-scrollbar {\n      width: 6px;\n      height: 6px;\n    }\n\n    ::-webkit-scrollbar-thumb {\n      background: #0a3054;\n      border-radius: 3px;\n    }\n\n    ::-webkit-scrollbar-track {\n      background: #1e415c;\n      border-radius: 3px;\n    }\n\n    // 设置表格背景透明\n    ::v-deep .el-table__body-wrapper {\n      background-color: transparent;\n\n      &::-webkit-scrollbar {\n        width: 6px;\n        height: 6px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #0a3054;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #1e415c;\n        border-radius: 3px;\n      }\n    }\n  }\n}\n\n.custom-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 9999;\n}\n\n.custom-modal {\n  width: 1300px;\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  border-radius: 8px;\n  padding: 0;\n  \n  .modal-header {\n    background: #1B2A47;\n    border-bottom: 1px solid #00E4FF;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .modal-title {\n      color: #00E4FF;\n      font-size: 18px;\n      font-weight: bold;\n    }\n\n    .header-buttons {\n      display: flex;\n      align-items: center;\n      \n      .close-text {\n        color: #00E4FF;\n        margin-right: 15px;\n      }\n\n      .close-btn {\n        color: #00E4FF;\n        font-size: 20px;\n        cursor: pointer;\n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n  }\n\n  .modal-content {\n    padding: 20px;\n    background: #1B2A47;\n\n    .search-container {\n      margin-bottom: 20px;\n      \n      .el-button--primary {\n        background: #1B2A47;\n        border: 1px solid #00E4FF;\n        color: #00E4FF;\n        \n        &:hover {\n          opacity: 0.8;\n        }\n      }\n    }\n\n    .el-table {\n      background: #1B2A47 !important;\n      border: 1px solid #00E4FF;\n      \n      &::before {\n        display: none;\n      }\n\n      th {\n        background: #162442 !important;\n        border-bottom: 1px solid #00E4FF !important;\n        color: #00E4FF !important;\n        font-weight: bold;\n      }\n\n      td {\n        background: #1B2A47 !important;\n        border-bottom: 1px solid rgba(0, 228, 255, 0.2) !important;\n        color: #fff !important;\n      }\n\n      .el-table__row:hover > td {\n        background: #243B6B !important;\n      }\n    }\n\n    .el-table--border::after {\n      display: none;\n    }\n  }\n}\n\n// 修改日期选择器样式\n:deep(.el-date-editor) {\n  background: #1B2A47;\n  border: 1px solid #00E4FF;\n  \n  .el-range-input {\n    background: #1B2A47;\n    color: #fff;\n  }\n  \n  .el-range-separator {\n    color: #00E4FF;\n  }\n}\n\n// 修改滚动条样式\n:deep(.el-table__body-wrapper::-webkit-scrollbar) {\n  width: 6px;\n  height: 6px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {\n  background: #00E4FF;\n  border-radius: 3px;\n}\n\n:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {\n  background: #1B2A47;\n}\n\n.title-container {\n  position: absolute;\n  top: -9px;\n  left: 57.5%;\n  width: 100%;\n  z-index: 1000;\n\n  .more-btn {\n    display: flex;\n    align-items: center;\n    cursor: pointer;\n    color: #2cc1ff;\n    font-size: 20px;\n\n    &:hover {\n      opacity: 0.8;\n    }\n\n    i {\n      margin-left: 5px;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;AA6WA,OAAO,KAAKA,IAAG,MAAO,MAAM;AAC5B,OAAOC,WAAU,MAAO,0CAA0C;AAClE,OAAOC,MAAK,MAAO,2CAA2C;AAC9D,OAAOC,OAAM,MAAO,4CAA4C;AAChE,OAAOC,MAAK,MAAO,iCAAiC;AAEpD,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,YAAW,MAAO,iDAAiD;AAC1E,OAAOC,QAAO,MAAO,qCAAqC;AAC1D,OAAOC,KAAI,MAAO,OAAO;AACzB,SAASC,sBAAqB,QAAS,cAAc;AAErD,eAAe;EACbC,UAAU,EAAE;IACVX,MAAM;IACNH,WAAW;IACXI,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,YAAY;IACZC,QAAQ;IACRV,MAAM;IACNC;EACF,CAAC;EACDa,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,MAAM,EAAE,IAAI;MACZC,aAAa,EAAE;QACbC,SAAS,EAAE,CACT;UACEC,IAAI,EAAE,MAAM;UACZC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,CAAC,CAAC;YACrDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC,EACD;UACEH,IAAI,EAAE,OAAO;UACbC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;YACtDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC,EACD;UACEH,IAAI,EAAE,OAAO;UACbC,OAAOA,CAACC,MAAM,EAAE;YACd,MAAMC,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;YACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;YACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAC,GAAI,EAAE,CAAC;YACtDL,MAAM,CAACM,KAAK,CAAC,MAAM,EAAE,CAACH,KAAK,EAAEF,GAAG,CAAC,CAAC;UACpC;QACF,CAAC;MAEL,CAAC;MACDM,OAAO,EAAE,CACP;QACEC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,OAAO;QACdC,KAAK,EAAE;MACT,CAAC,CACF;MACDC,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEH,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEL,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDK,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE,CACR;QACEP,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE;MACT,CAAC,CACF;MACDG,YAAY,EAAE,KAAK;MACnBI,QAAQ,EAAE,CACR;QACER,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,EACD;QACED,KAAK,EAAE,KAAK;QACZC,KAAK,EAAE;MACT,CAAC,CACF;MACDQ,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE,CACV;QACEC,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDC,WAAW,EAAE,CACX;QACEJ,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,EACD;QACEH,IAAI,EAAE,MAAM;QACZX,KAAK,EAAE,EAAE;QACTY,SAAS,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAI;MAC9C,CAAC,CACF;MACDE,KAAK,EAAE;QACLC,SAAS,EAAE,EAAE;QACbC,UAAU,EAAE,EAAE;QACdC,UAAU,EAAE;MACd,CAAC;MACDC,OAAO,EAAE,QAAQ;MAAE;MACnB;MACAC,YAAY,EAAE,KAAK;MACnBC,gBAAgB,EAAE;QAChBC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,KAAK,EAAE,CAAC,CAAE;MACZ,CAAC;MACDC,WAAW,EAAE,IAAI;MAAE;MACnBC,mBAAmB,EAAE,CAAC;MACtBC,OAAO,EAAE,EAAE;MAAE;MACbC,eAAe,EAAE;QACfC,SAAS,EAAE,CAAC;QACZN,OAAO,EAAE,CAAC;QACVO,MAAM,EAAE;MACV,CAAC;MACDC,aAAa,EAAE,EAAE;MAAE;MACnBC,oBAAoB,EAAE,EAAE;MAAE;;MAE1BC,aAAa,EAAE,KAAK;MAAE;MACtBC,SAAS,EAAE,EAAE;MACbC,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE,EAAE,CAAE;IAC1B,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACPC,KAAKA,CAAA,EAAG;MACN,IAAI,CAACrD,MAAK,GAAI,KAAK;IACrB,CAAC;IAED;IACAsD,WAAWA,CAACC,IAAI,EAAE;MAChB,IAAI,CAAC,IAAI,CAACf,WAAU,IAAK,CAAC,IAAI,CAACA,WAAW,CAACgB,EAAE,EAAE;QAC7CC,OAAO,CAACC,KAAK,CAAC,kBAAkB,CAAC;QACjC,OAAO,IAAI;MACb;MAEA,MAAMC,KAAI,GAAI;QACZC,UAAU,EAAE,IAAI,CAACpB,WAAW,CAACgB,EAAE;QAC/BK,UAAU,EAAE,aAAa;QACzBN,IAAI,EAAE,aAAa;QACnBO,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,IAAI,CAACC,OAAO,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;QACxDC,EAAE,EAAE,IAAI,CAACH,OAAO,CAAC,CAAC,CAACE,MAAM,CAAC,YAAY;MACxC,CAAC;;MAED;MACA,QAAQX,IAAI;QACV,KAAK,OAAO;UACV;UACAI,KAAK,CAACG,WAAU,GAAI,KAAK;UACzBH,KAAK,CAACI,IAAG,GAAI,IAAI,CAACC,OAAO,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACnEP,KAAK,CAACQ,EAAC,GAAI,IAAI,CAACH,OAAO,CAAC,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;UAC9C;QACF,KAAK,QAAQ;UACX;UACAP,KAAK,CAACG,WAAU,GAAI,KAAK;UACzBH,KAAK,CAACI,IAAG,GAAI,IAAI,CAACC,OAAO,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACpEP,KAAK,CAACQ,EAAC,GAAI,IAAI,CAACH,OAAO,CAAC,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;UAC9C;QACF,KAAK,SAAS;UACZ;UACAP,KAAK,CAACG,WAAU,GAAI,KAAK;UACzBH,KAAK,CAACI,IAAG,GAAI,IAAI,CAACC,OAAO,CAAC,CAAC,CAACI,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAACF,MAAM,CAAC,YAAY,CAAC;UACrEP,KAAK,CAACQ,EAAC,GAAI,IAAI,CAACH,OAAO,CAAC,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;UAC9C;QACF,KAAK,OAAO;UACV;UACAP,KAAK,CAACG,WAAU,GAAI,OAAO;UAC3BH,KAAK,CAACI,IAAG,GAAI,IAAI,CAACC,OAAO,CAAC,CAAC,CAACI,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CAACH,OAAO,CAAC,MAAM,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;UACtFP,KAAK,CAACQ,EAAC,GAAI,IAAI,CAACH,OAAO,CAAC,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC;UAC9C;MACJ;MAEA,OAAOP,KAAK;IACd,CAAC;IAED;IACA,MAAMU,mBAAmBA,CAACd,IAAI,EAAE;MAC9B,MAAMI,KAAI,GAAI,IAAI,CAACL,WAAW,CAACC,IAAI,CAAC;MACpC,IAAI,CAACI,KAAK,EAAE;QACV,OAAO,CAAC;MACV;MAEA,IAAI;QACF,MAAMW,GAAE,GAAI,MAAMzE,sBAAsB,CAAC8D,KAAK,CAAC;QAC/C,IAAIpB,KAAI,GAAI,CAAC;QACb,IAAI+B,GAAG,CAACvE,IAAG,IAAKuE,GAAG,CAACvE,IAAI,CAACwE,KAAK,EAAE;UAC9BhC,KAAI,GAAI+B,GAAG,CAACvE,IAAI,CAACwE,KAAK,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAE,GAAIE,QAAQ,CAACD,CAAC,CAACE,QAAO,IAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/E;QACA,OAAOrC,KAAK;MACd,EAAE,OAAOmB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,KAAKH,IAAI,OAAO,EAAEG,KAAK,CAAC;QACtC,OAAO,CAAC;MACV;IACF,CAAC;IACD,MAAMmB,QAAQA,CAAA,EAAG;MACf,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMlF,KAAK,CAACmF,GAAG,CAC9B,GAAG,IAAI,CAAC9C,OAAO,yBAAyB,EACxC;UACE+C,MAAM,EAAE;YACNlD,SAAS,EAAE;UACb;QACF,CACF,CAAC;QAED,IAAIgD,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,MAAM;YAAEnD,SAAS;YAAEC,UAAU;YAAEC;UAAW,IACxC8C,QAAQ,CAAC/E,IAAI,CAACmF,WAAW;UAC3B,IAAI,CAACrD,KAAI,GAAI;YACXC,SAAS;YACTC,UAAU;YACVC;UACF,CAAC;UACD,IAAI,CAACE,YAAW,GAAI,IAAI;;UAExB;UACA,MAAMiD,SAAQ,GAAI;YAChB,GAAG,IAAI,CAACtD,KAAK;YACbuD,SAAS,EAAE,IAAI7E,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC;UAChC,CAAC;UACD2E,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACL,SAAS,CAAC,CAAC;UAE7D1B,OAAO,CAACgC,GAAG,CAAC,6BAA6B,EAAE,IAAI,CAAC5D,KAAK,CAAC;QACxD,OAAO;UACL4B,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAAM,CAAC;UAC3D,IAAI,CAACxD,YAAW,GAAI,KAAK;QAC3B;MACF,EAAE,OAAOwB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAACxB,YAAW,GAAI,KAAK;MAC3B;IACF,CAAC;IACDyD,kBAAkBA,CAAA,EAAG;MACnB,MAAMC,WAAU,GAAIP,YAAY,CAACQ,OAAO,CAAC,YAAY,CAAC;MACtD,IAAID,WAAW,EAAE;QACf,MAAMT,SAAQ,GAAII,IAAI,CAACO,KAAK,CAACF,WAAW,CAAC;QACzC,MAAMG,UAAS,GAAI,IAAIxF,IAAI,CAAC4E,SAAS,CAACnD,UAAU,CAAC,CAACtB,OAAO,CAAC,CAAC;QAC3D,MAAMsF,WAAU,GAAI,IAAIzF,IAAI,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC;QACxC,MAAMuF,cAAa,GAAId,SAAS,CAACC,SAAS;;QAE1C;QACA,IACEY,WAAU,GAAID,UAAS,IACvBC,WAAU,GAAIC,cAAa,GAAI,IAAI,EAAC,GAAI,IAAG,EAC3C;UACA,IAAI,CAACpE,KAAI,GAAI;YACXC,SAAS,EAAEqD,SAAS,CAACrD,SAAS;YAC9BC,UAAU,EAAEoD,SAAS,CAACpD,UAAU;YAChCC,UAAU,EAAEmD,SAAS,CAACnD;UACxB,CAAC;UACD,IAAI,CAACE,YAAW,GAAI,IAAI;UACxB,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd,CAAC;IACD,MAAMgE,mBAAmBA,CAACC,SAAS,EAAEC,OAAO,EAAE;MAC5C,IAAI,CAAC,IAAI,CAAClE,YAAY,EAAE;QACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAEH,SAAS;UACpBI,OAAO,EAAEH,OAAO;UAChB1D,OAAO,EAAE,IAAI,CAACA,OAAO,CAAE;QACzB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B;UACA,MAAM9C,gBAAe,GAAI2C,QAAQ,CAAC/E,IAAI,CAACmF,WAAW,CAACV,MAAM,CACvD,CAACC,GAAG,EAAE+B,IAAI,KAAK;YACb,OAAO/B,GAAE,GAAIgC,UAAU,CAACD,IAAI,CAACE,OAAM,IAAK,CAAC,CAAC;UAC5C,CAAC,EACD,CACF,CAAC;UACD,OAAOvE,gBAAgB;QACzB,OAAO;UACLsB,OAAO,CAACC,KAAK,CACX,kCAAkC,EAClCoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAChB,CAAC;UACD,OAAO,CAAC;QACV;MACF,EAAE,OAAOhC,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,OAAO,CAAC;MACV;IACF,CAAC;IACDiD,UAAUA,CAACC,IAAI,EAAE;MACf,MAAMC,IAAG,GAAID,IAAI,CAACE,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAI,GAAIC,MAAM,CAACJ,IAAI,CAACK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAE,GAAIH,MAAM,CAACJ,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAI,GAAIL,MAAM,CAACJ,IAAI,CAACU,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,OAAM,GAAIP,MAAM,CAACJ,IAAI,CAACY,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMO,OAAM,GAAIT,MAAM,CAACJ,IAAI,CAACc,UAAU,CAAC,CAAC,CAAC,CAACR,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;IACjE,CAAC;IACD,MAAME,oBAAoBA,CAACxB,SAAS,EAAEC,OAAO,EAAE;MAC7C,IAAI,CAAC,IAAI,CAAClE,YAAY,EAAE;QACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAEH,SAAS;UACpBI,OAAO,EAAEH,OAAO;UAChB1D,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,MAAM2C,QAAO,GAAI9C,QAAQ,CAAC/E,IAAI,CAACmF,WAAW,CAACV,MAAM,CAAC,CAACC,GAAG,EAAE+B,IAAI,KAAK;YAC/D,OAAO/B,GAAE,GAAIgC,UAAU,CAACD,IAAI,CAACqB,GAAE,IAAK,CAAC,CAAC;UACxC,CAAC,EAAE,CAAC,CAAC;UACL,OAAOD,QAAQ;QACjB,OAAO;UACLnE,OAAO,CAACC,KAAK,CACX,mCAAmC,EACnCoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAChB,CAAC;UACD,OAAO,CAAC;QACV;MACF,EAAE,OAAOhC,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,OAAO,CAAC;MACV;IACF,CAAC;IACD,MAAMoE,qBAAqBA,CAAA,EAAG;MAC5BrE,OAAO,CAACgC,GAAG,CAAC,IAAI,CAAC;;MAEjB;MACA,IAAI,CAAC,IAAI,CAACjD,WAAU,IAAK,CAAC,IAAI,CAACA,WAAW,CAACgB,EAAE,EAAE;QAC7CC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;QAC1C;MACF;MAEA,IAAI;QACF;QACA;QACA,IAAI,CAACvB,gBAAgB,CAACI,KAAI,GAAI,MAAM,IAAI,CAAC8B,mBAAmB,CAAC,OAAO,CAAC;;QAErE;QACA,IAAI,CAAClC,gBAAgB,CAACC,KAAI,GAAI,MAAM,IAAI,CAACiC,mBAAmB,CAAC,OAAO,CAAC;;QAErE;QACA,IAAI,CAAClC,gBAAgB,CAACE,MAAK,GAAI,MAAM,IAAI,CAACgC,mBAAmB,CAAC,QAAQ,CAAC;;QAEvE;QACA,IAAI,CAAClC,gBAAgB,CAACG,OAAM,GAAI,MAAM,IAAI,CAAC+B,mBAAmB,CAAC,SAAS,CAAC;QAEzEZ,OAAO,CAACgC,GAAG,CAAC,WAAW,EAAE,IAAI,CAACtD,gBAAgB,CAAC;MACjD,EAAE,OAAOuB,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC;QACA,MAAM,IAAI,CAACqE,6BAA6B,CAAC,CAAC;MAC5C;IACF,CAAC;IAED;IACA,MAAMA,6BAA6BA,CAAA,EAAG;MACpC,MAAMC,GAAE,GAAI,IAAIzH,IAAI,CAAC,CAAC;MACtB,MAAM0H,KAAI,GAAI,IAAI1H,IAAI,CAACyH,GAAG,CAAClB,WAAW,CAAC,CAAC,EAAEkB,GAAG,CAACf,QAAQ,CAAC,CAAC,EAAEe,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMxE,SAAQ,GAAI,IAAIrC,IAAI,CAAC0H,KAAK,CAAC;MACjCrF,SAAS,CAACsF,OAAO,CAACtF,SAAS,CAACwE,OAAO,CAAC,IAAI,CAAC,CAAC;MAC1C,MAAMe,OAAM,GAAI,IAAI5H,IAAI,CAAC0H,KAAK,CAAC;MAC/BE,OAAO,CAACD,OAAO,CAACC,OAAO,CAACf,OAAO,CAAC,IAAI,CAAC,CAAC;MACtC,MAAMgB,QAAO,GAAI,IAAI7H,IAAI,CAAC0H,KAAK,CAAC;MAChCG,QAAQ,CAACF,OAAO,CAACE,QAAQ,CAAChB,OAAO,CAAC,IAAI,EAAE,CAAC;;MAEzC;MACA,IAAI,CAACjF,gBAAgB,CAACC,KAAI,GAAI,MAAM,IAAI,CAAC8D,mBAAmB,CAC1D,IAAI,CAACS,UAAU,CAAC/D,SAAS,CAAC,EAC1B,IAAI,CAAC+D,UAAU,CAACqB,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC7F,gBAAgB,CAACE,MAAK,GAAI,MAAM,IAAI,CAAC6D,mBAAmB,CAC3D,IAAI,CAACS,UAAU,CAACwB,OAAO,CAAC,EACxB,IAAI,CAACxB,UAAU,CAACqB,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC7F,gBAAgB,CAACG,OAAM,GAAI,MAAM,IAAI,CAAC4D,mBAAmB,CAC5D,IAAI,CAACS,UAAU,CAACyB,QAAQ,CAAC,EACzB,IAAI,CAACzB,UAAU,CAACqB,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAAC7F,gBAAgB,CAACI,KAAI,GAAI,IAAI,CAACJ,gBAAgB,CAACG,OAAO;;MAE3D;MACA,IAAI,CAACG,mBAAkB,GAAI,MAAM,IAAI,CAACkF,oBAAoB,CACxD,IAAI,CAAChB,UAAU,CAACyB,QAAQ,CAAC,EACzB,IAAI,CAACzB,UAAU,CAACqB,GAAG,CACrB,CAAC;IACH,CAAC;IACD,MAAMK,aAAaA,CAAA,EAAG;MACpB,MAAML,GAAE,GAAI,IAAIzH,IAAI,CAAC,CAAC;MACtB,MAAM0H,KAAI,GAAI,IAAI1H,IAAI,CAACyH,GAAG,CAAClB,WAAW,CAAC,CAAC,EAAEkB,GAAG,CAACf,QAAQ,CAAC,CAAC,EAAEe,GAAG,CAACZ,OAAO,CAAC,CAAC,CAAC;MACxE,MAAMxE,SAAQ,GAAI,IAAIrC,IAAI,CAAC0H,KAAK,CAAC;MACjCrF,SAAS,CAACsF,OAAO,CAACtF,SAAS,CAACwE,OAAO,CAAC,IAAI,CAAC,CAAC;MAC1C,MAAMkB,UAAS,GAAI,IAAI/H,IAAI,CAAC0H,KAAK,CAACnB,WAAW,CAAC,CAAC,EAAEmB,KAAK,CAAChB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,MAAMsB,SAAQ,GAAI,IAAIhI,IAAI,CAAC0H,KAAK,CAACnB,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;;MAErD;MACA,IAAI,CAACnE,eAAe,CAACC,SAAQ,GAAI,MAAM,IAAI,CAAC+E,oBAAoB,CAC9D,IAAI,CAAChB,UAAU,CAAC/D,SAAS,CAAC,EAC1B,IAAI,CAAC+D,UAAU,CAACsB,KAAK,CACvB,CAAC;;MAED;MACA,IAAI,CAACtF,eAAe,CAACL,OAAM,GAAI,MAAM,IAAI,CAACqF,oBAAoB,CAC5D,IAAI,CAAChB,UAAU,CAAC2B,UAAU,CAAC,EAC3B,IAAI,CAAC3B,UAAU,CAACqB,GAAG,CACrB,CAAC;;MAED;MACA,IAAI,CAACrF,eAAe,CAACE,MAAK,GAAI,MAAM,IAAI,CAAC8E,oBAAoB,CAC3D,IAAI,CAAChB,UAAU,CAAC4B,SAAS,CAAC,EAC1B,IAAI,CAAC5B,UAAU,CAACqB,GAAG,CACrB,CAAC;IACH,CAAC;IACD,MAAMQ,gBAAgBA,CAAA,EAAG;MACvB,IAAI,CAAC,IAAI,CAACtG,YAAY,EAAE;QACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMmD,GAAE,GAAI,IAAIzH,IAAI,CAAC,CAAC;QACtB,MAAMkI,YAAW,GAAI,IAAIlI,IAAI,CAACyH,GAAG,CAAC;QAClCS,YAAY,CAACP,OAAO,CAACO,YAAY,CAACrB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMtC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAE,IAAI,CAACK,UAAU,CAAC8B,YAAY,CAAC;UACxClC,OAAO,EAAE,IAAI,CAACI,UAAU,CAACqB,GAAG,CAAC;UAC7BU,GAAG,EAAE,CAAC;UAAE;UACRC,MAAM,EAAE,CAAC;UAAE;UACXjG,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAACnC,aAAY,GAAIgC,QAAQ,CAAC/E,IAAI,CAACmF,WAAW;UAC9CzB,OAAO,CAACgC,GAAG,CACT,wCAAwC,EACxC,IAAI,CAAC3C,aACP,CAAC;QACH,OAAO;UACLW,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAAM,CAAC;QACtE;MACF,EAAE,OAAOhC,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF,CAAC;IACD,MAAMkF,uBAAuBA,CAAA,EAAG;MAC9B,IAAI,CAAC,IAAI,CAAC1G,YAAY,EAAE;QACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMmD,GAAE,GAAI,IAAIzH,IAAI,CAAC,CAAC;QACtB,MAAMkI,YAAW,GAAI,IAAIlI,IAAI,CAACyH,GAAG,CAAC;QAClCS,YAAY,CAACP,OAAO,CAACO,YAAY,CAACrB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMtC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAE,IAAI,CAACK,UAAU,CAAC8B,YAAY,CAAC;UACxClC,OAAO,EAAE,IAAI,CAACI,UAAU,CAACqB,GAAG,CAAC;UAC7BtF,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAAClC,oBAAmB,GAAI+B,QAAQ,CAAC/E,IAAI,CAACmF,WAAU,CACjD2D,GAAG,CAAErC,IAAI,KAAM;YACd,GAAGA,IAAI;YACPE,OAAO,EAAED,UAAU,CAACD,IAAI,CAACE,OAAM,IAAK,CAAC;UACvC,CAAC,CAAC,EACDoC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzI,IAAI,CAACwI,CAAC,CAACE,QAAQ,IAAI,IAAI1I,IAAI,CAACyI,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC9DxF,OAAO,CAACgC,GAAG,CACT,gDAAgD,EAChD,IAAI,CAAC1C,oBACP,CAAC;QACH,OAAO;UACLU,OAAO,CAACC,KAAK,CACX,kCAAkC,EAClCoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAChB,CAAC;QACH;MACF,EAAE,OAAOhC,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IACDwF,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAAClG,aAAY,GAAI,IAAI;MACzB,IAAI,CAACE,UAAS,GAAI,EAAE,EAAE;MACtB;MACA,MAAM5C,GAAE,GAAI,IAAIC,IAAI,CAAC,CAAC;MACtB,MAAMC,KAAI,GAAI,IAAID,IAAI,CAAC,CAAC;MACxBC,KAAK,CAACC,OAAO,CAACD,KAAK,CAACE,OAAO,CAAC,IAAI,IAAG,GAAI,IAAG,GAAI,EAAE,CAAC,EAAE;MACnD,IAAI,CAACuC,SAAQ,GAAI,CACf,IAAI,CAAC0D,UAAU,CAACnG,KAAK,CAAC,CAAC2I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EACpC,IAAI,CAACxC,UAAU,CAACrG,GAAG,CAAC,CAAC6I,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnC;MACD,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;IACrB,CAAC;IACD,MAAMA,UAAUA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACnG,SAAQ,IAAK,IAAI,CAACA,SAAS,CAACoG,MAAK,KAAM,CAAC,EAAE;QAClD,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,IAAI;QACF,IAAI,CAAC,IAAI,CAACrH,YAAY,EAAE;UACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;QACvB;QAEA,MAAMC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,6BAA6B,EAC5C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAE,GAAG,IAAI,CAACrD,SAAS,CAAC,CAAC,CAAC,WAAW;UAC1CsD,OAAO,EAAE,GAAG,IAAI,CAACtD,SAAS,CAAC,CAAC,CAAC,WAAW;UACxCP,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,IACEH,QAAQ,CAAC/E,IAAI,CAACmF,WAAU,IACxBJ,QAAQ,CAAC/E,IAAI,CAACmF,WAAW,CAACmE,MAAK,GAAI,GACnC;YACA,IAAI,CAACnG,UAAS,GAAI4B,QAAQ,CAAC/E,IAAI,CAACmF,WAAU,CACvC2D,GAAG,CAAErC,IAAI,KAAM;cACd9D,OAAO,EAAE8D,IAAI,CAAC9D,OAAM,IAAK,EAAE;cAC3B8G,SAAS,EAAEhD,IAAI,CAACgD,SAAQ,IAAK,EAAE;cAC/BC,SAAS,EAAEjD,IAAI,CAACiD,SAAQ,GACpBhD,UAAU,CAACD,IAAI,CAACiD,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,IACpC,KAAK;cACTC,OAAO,EAAEnD,IAAI,CAACmD,OAAM,GAChBlD,UAAU,CAACD,IAAI,CAACmD,OAAO,CAAC,CAACD,OAAO,CAAC,CAAC,IAClC,KAAK;cACThD,OAAO,EAAEF,IAAI,CAACE,OAAM,GAChBD,UAAU,CAACD,IAAI,CAACE,OAAO,CAAC,CAACgD,OAAO,CAAC,CAAC,IAClC,MAAM;cACVE,IAAI,EAAEpD,IAAI,CAACoD,IAAG,IAAK,EAAE;cACrBrD,OAAO,EAAEC,IAAI,CAACD,OAAM,GAAIC,IAAI,CAACD,OAAO,CAACsD,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;YAC5D,CAAC,CAAC,EACDf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzI,IAAI,CAACyI,CAAC,CAACzC,OAAO,IAAI,IAAIhG,IAAI,CAACwI,CAAC,CAACxC,OAAO,CAAC,CAAC;YAC5D,IAAI,CAAC+C,QAAQ,CAACQ,OAAO,CAAC,MAAM,CAAC;UAC/B,OAAO;YACL,IAAI,CAAC5G,UAAS,GAAI,EAAE;YACpB,IAAI,CAACoG,QAAQ,CAACC,OAAO,CAAC,YAAY,CAAC;UACrC;QACF,OAAO;UACL,IAAI,CAACD,QAAQ,CAAC5F,KAAK,CAAC,SAAQ,GAAIoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAAM,CAAC;UACrD,IAAI,CAACxC,UAAS,GAAI,EAAE;QACtB;MACF,EAAE,OAAOQ,KAAK,EAAE;QACd,IAAI,CAAC4F,QAAQ,CAAC5F,KAAK,CAAC,SAAQ,IAAKA,KAAK,CAACqG,OAAM,IAAK,MAAM,CAAC,CAAC;QAC1D,IAAI,CAAC7G,UAAS,GAAI,EAAE;MACtB;IACF,CAAC;IACD8G,aAAaA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAAC9G,UAAS,IAAK,CAAC,IAAI,CAACA,UAAU,CAACmG,MAAM,EAAE;QAC/C,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,SAAS,CAAC;QAChC;MACF;MAEA,IAAI;QACF;QACA,MAAMU,UAAS,GAAI,IAAI,CAAC/G,UAAU,CAAC2F,GAAG,CAAErC,IAAI,KAAM;UAChD0D,IAAI,EAAE1D,IAAI,CAAC9D,OAAM,IAAK,EAAE;UACxByH,IAAI,EAAE3D,IAAI,CAACgD,SAAQ,IAAK,EAAE;UAC1BY,EAAE,EAAE5D,IAAI,CAACiD,SAAQ,GAAIhD,UAAU,CAACD,IAAI,CAACiD,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,IAAI,KAAK;UAClEW,EAAE,EAAE7D,IAAI,CAACmD,OAAM,GAAIlD,UAAU,CAACD,IAAI,CAACmD,OAAO,CAAC,CAACD,OAAO,CAAC,CAAC,IAAI,KAAK;UAC9D,UAAU,EAAElD,IAAI,CAACE,OAAM,GACnBD,UAAU,CAACD,IAAI,CAACE,OAAO,CAAC,CAACgD,OAAO,CAAC,CAAC,IAClC,MAAM;UACVY,IAAI,EAAE9D,IAAI,CAACoD,IAAG,IAAK,EAAE;UACrBW,IAAI,EAAE/D,IAAI,CAACD,OAAM,GAAIC,IAAI,CAACD,OAAO,CAACsD,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;QACzD,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMW,EAAC,GAAIzL,IAAI,CAAC0L,KAAK,CAACC,aAAa,CAACT,UAAU,CAAC;QAC/C,MAAMU,EAAC,GAAI5L,IAAI,CAAC0L,KAAK,CAACG,QAAQ,CAAC,CAAC;QAChC7L,IAAI,CAAC0L,KAAK,CAACI,iBAAiB,CAACF,EAAE,EAAEH,EAAE,EAAE,OAAO,CAAC;;QAE7C;QACAA,EAAE,CAAC,OAAO,IAAI,CACZ;UAAEM,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC;QAAE;QACb;UAAEA,GAAG,EAAE;QAAG,CAAC,CAAE;QAAA,CACd;;QAED;QACA,MAAMC,QAAO,GAAI,SAAS,IAAI,CAAC9H,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAACA,SAAS,CAAC,CAAC,CAAC,OAAO;QACvElE,IAAI,CAACiM,SAAS,CAACL,EAAE,EAAEI,QAAQ,CAAC;QAE5B,IAAI,CAACzB,QAAQ,CAACQ,OAAO,CAAC,MAAM,CAAC;MAC/B,EAAE,OAAOpG,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC,IAAI,CAAC4F,QAAQ,CAAC5F,KAAK,CAAC,OAAM,IAAKA,KAAK,CAACqG,OAAM,IAAK,MAAM,CAAC,CAAC;MAC1D;IACF,CAAC;IACD,MAAMkB,qBAAqBA,CAAA,EAAG;MAC5B,IAAI,CAAC,IAAI,CAAC/I,YAAY,EAAE;QACtB,MAAM,IAAI,CAAC2C,QAAQ,CAAC,CAAC;MACvB;MAEA,IAAI;QACF,MAAMmD,GAAE,GAAI,IAAIzH,IAAI,CAAC,CAAC;QACtB,MAAMkI,YAAW,GAAI,IAAIlI,IAAI,CAACyH,GAAG,CAAC;QAClCS,YAAY,CAACP,OAAO,CAACO,YAAY,CAACrB,OAAO,CAAC,IAAI,CAAC,CAAC;QAEhD,MAAMtC,QAAO,GAAI,MAAMlF,KAAK,CAACyG,IAAI,CAC/B,GAAG,IAAI,CAACpE,OAAO,8BAA8B,EAC7C;UACEF,UAAU,EAAE,IAAI,CAACF,KAAK,CAACE,UAAU;UACjCuE,SAAS,EAAE,IAAI,CAACK,UAAU,CAAC8B,YAAY,CAAC;UACxClC,OAAO,EAAE,IAAI,CAACI,UAAU,CAACqB,GAAG,CAAC;UAC7BtF,OAAO,EAAE,IAAI,CAACA;QAChB,CACF,CAAC;QAED,IAAIoC,QAAQ,CAAC/E,IAAI,CAACkF,OAAM,KAAM,CAAC,EAAE;UAC/B,IAAI,CAAC9B,kBAAiB,GAAI2B,QAAQ,CAAC/E,IAAI,CAACmF,WAAU,CAC/C2D,GAAG,CAAErC,IAAI,KAAM;YACd,GAAGA,IAAI;YACPqB,GAAG,EAAEpB,UAAU,CAACD,IAAI,CAACqB,GAAE,IAAK,CAAC;UAC/B,CAAC,CAAC,EACDiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIzI,IAAI,CAACwI,CAAC,CAACE,QAAQ,IAAI,IAAI1I,IAAI,CAACyI,CAAC,CAACC,QAAQ,CAAC,CAAC;UAC9DxF,OAAO,CAACgC,GAAG,CACT,8CAA8C,EAC9C,IAAI,CAACtC,kBACP,CAAC;QACH,OAAO;UACLM,OAAO,CAACC,KAAK,CACX,qCAAqC,EACrCoB,QAAQ,CAAC/E,IAAI,CAAC2F,MAChB,CAAC;QACH;MACF,EAAE,OAAOhC,KAAK,EAAE;QACdD,OAAO,CAACC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC7D;IACF;EACF,CAAC;EACD,MAAMwH,OAAOA,CAAA,EAAG;IACd;IACA,IAAI;MACF,IAAI,CAAC1I,WAAU,GAAI,IAAI,CAAC2I,EAAE,CAACC,cAAc,CAAC,CAAC;MAC3C,IAAI,CAAC,IAAI,CAAC5I,WAAU,IAAK,CAAC,IAAI,CAACA,WAAW,CAACgB,EAAE,EAAE;QAC7CC,OAAO,CAAC4H,IAAI,CAAC,sBAAsB,CAAC;QACpC;QACA,IAAI,CAAC7I,WAAU,GAAI;UAAEgB,EAAE,EAAE;QAAE,CAAC,EAAE;MAChC;IACF,EAAE,OAAOE,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC;MACA,IAAI,CAAClB,WAAU,GAAI;QAAEgB,EAAE,EAAE;MAAE,CAAC;IAC9B;;IAEA;IACA,IAAI,CAAC,IAAI,CAACmC,kBAAkB,CAAC,CAAC,EAAE;MAC9B,MAAM,IAAI,CAACd,QAAQ,CAAC,CAAC;IACvB;;IAEA;IACA,MAAM,IAAI,CAACiD,qBAAqB,CAAC,CAAC;IAClC,MAAM,IAAI,CAACO,aAAa,CAAC,CAAC;IAC1B,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;IAC7B,MAAM,IAAI,CAACI,uBAAuB,CAAC,CAAC;IACpC,MAAM,IAAI,CAACqC,qBAAqB,CAAC,CAAC;;IAElC;IACAK,WAAW,CAAC,YAAY;MACtB,MAAM,IAAI,CAACzG,QAAQ,CAAC,CAAC;IACvB,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC;;IAEjB;IACAyG,WAAW,CAAC,YAAY;MACtB,MAAM,IAAI,CAACxD,qBAAqB,CAAC,CAAC;MAClC,MAAM,IAAI,CAACO,aAAa,CAAC,CAAC;MAC1B,MAAM,IAAI,CAACG,gBAAgB,CAAC,CAAC;MAC7B,MAAM,IAAI,CAACI,uBAAuB,CAAC,CAAC;MACpC,MAAM,IAAI,CAACqC,qBAAqB,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,EAAC,GAAI,IAAI,CAAC;EACnB;AACF,CAAC", "ignoreList": []}]}